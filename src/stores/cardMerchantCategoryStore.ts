import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import {
  type CardMerchantCategoryRestrictionOption,
  type CardMerchantCategoryStoreState,
  type MerchantCategoryState,
  type MerchantType,
} from 'features/card/types/CardMerchantCategory.ts';
import { type Merchant } from 'features/debit/types/Merchant.ts';

const createInitialMerchantCategoryState = (): MerchantCategoryState => ({
  // Restriction modal state
  isRestrictionsModalVisible: false,
  selectedRestrictionOption: undefined,

  // Category modal state
  isCategoryModalVisible: false,
  searchText: '',

  // Selection state
  selectedMerchantCategoryUuids: [],
  selectedMerchantCategoryUuid: '',
  areAllMerchantCategoriesSelected: false,
  excludeSelectedMerchantCategoryUuids: [],

  // Data state
  localMerchantCategories: [],
  selectedMultipleMerchantCategories: [],
  selectedSingleMerchantCategory: [],

  // UI state
  categoriesCount: 0,
  categoriesTotal: 0,
  isHideChoiceChips: false,
  listChangedCount: 0,
  isFetching: false,
});

export const useCardMerchantCategoryStore = defineStore('cardMerchantCategory', () => {
  // State
  const state = ref<CardMerchantCategoryStoreState>({
    merchantCategoryLocks: createInitialMerchantCategoryState(),
    merchantLocks: createInitialMerchantCategoryState(),
  });

  // Getters - Readonly computed properties
  const getMerchantCategoryLocksState = computed(() => state.value.merchantCategoryLocks);
  const getMerchantLocksState = computed(() => state.value.merchantLocks);

  const getStateByType = computed(() => (
    type: MerchantType,
  ): MerchantCategoryState => state.value[type]);

  const getIsRestrictionsModalVisible = computed(
    () => (type: MerchantType): boolean => state.value[type].isRestrictionsModalVisible,
  );

  const getSelectedRestrictionOption = computed(
    () => (
      type: MerchantType,
    ): CardMerchantCategoryRestrictionOption | undefined => state.value[type]
      .selectedRestrictionOption,
  );

  const getIsCategoryModalVisible = computed(
    () => (type: MerchantType): boolean => state.value[type].isCategoryModalVisible,
  );

  const getSearchText = computed(
    () => (type: MerchantType): string => state.value[type].searchText,
  );

  const getSelectedMerchantCategoryUuids = computed(
    () => (type: MerchantType): string[] => state.value[type].selectedMerchantCategoryUuids,
  );

  const getSelectedMerchantCategoryUuid = computed(() => (
    type: MerchantType,
  ): string => state.value[type].selectedMerchantCategoryUuid);

  const getAreAllMerchantCategoriesSelected = computed(
    () => (type: MerchantType): boolean | null => state.value[type]
      .areAllMerchantCategoriesSelected,
  );

  const getExcludeSelectedMerchantCategoryUuids = computed(
    () => (type: MerchantType): string[] => state.value[type].excludeSelectedMerchantCategoryUuids,
  );

  const getLocalMerchantCategories = computed(() => (
    type: MerchantType,
  ): Merchant[] => state.value[type].localMerchantCategories);

  const getSelectedMultipleMerchantCategories = computed(
    () => (type: MerchantType): Merchant[] => state.value[type].selectedMultipleMerchantCategories,
  );

  const getSelectedSingleMerchantCategory = computed(
    () => (type: MerchantType): Merchant[] => state.value[type].selectedSingleMerchantCategory,
  );

  const getCategoriesCount = computed(
    () => (type: MerchantType): number => state.value[type].categoriesCount,
  );

  const getCategoriesTotal = computed(
    () => (type: MerchantType): number => state.value[type].categoriesTotal,
  );

  const getIsHideChoiceChips = computed(
    () => (type: MerchantType): boolean => state.value[type].isHideChoiceChips,
  );

  const getListChangedCount = computed(
    () => (type: MerchantType): number => state.value[type].listChangedCount,
  );

  const getIsFetching = computed(
    () => (type: MerchantType): boolean => state.value[type].isFetching,
  );

  // Computed getters for derived state
  const getSelectedMerchantCategory = computed(() => (type: MerchantType): Merchant | undefined => {
    const selectedUuid = state.value[type].selectedMerchantCategoryUuid;

    if (!selectedUuid) {
      return undefined;
    }

    return state.value[type].selectedSingleMerchantCategory.find(
      (category) => category.uuid === selectedUuid,
    );
  });

  const getIsSelectedLabelVisible = computed(
    () => (
      type: MerchantType,
      isMasterCardFlow?: boolean,
    ): boolean => {
      if (isMasterCardFlow) {
        return !!state.value[type].selectedMerchantCategoryUuid;
      }

      return !!state.value[type].selectedMerchantCategoryUuids.length;
    },
  );

  const getAreAllCategoriesVisible = computed(() => (type: MerchantType): boolean => {
    const merchantState = state.value[type];

    return merchantState.areAllMerchantCategoriesSelected !== false
      && merchantState.selectedMultipleMerchantCategories.length === merchantState.categoriesTotal;
  });

  // Actions - Setter functions with arrow syntax
  const setIsRestrictionsModalVisible = (type: MerchantType, value: boolean): void => {
    state.value[type].isRestrictionsModalVisible = value;
  };

  const setSelectedRestrictionOption = (
    type: MerchantType,
    option: CardMerchantCategoryRestrictionOption | undefined,
  ): void => {
    state.value[type].selectedRestrictionOption = option;
  };

  const setIsCategoryModalVisible = (type: MerchantType, value: boolean): void => {
    state.value[type].isCategoryModalVisible = value;
  };

  const setSearchText = (type: MerchantType, value: string): void => {
    state.value[type].searchText = value;
  };

  const setSelectedMerchantCategoryUuids = (type: MerchantType, uuids: string[]): void => {
    state.value[type].selectedMerchantCategoryUuids = uuids;
  };

  const setSelectedMerchantCategoryUuid = (type: MerchantType, uuid: string): void => {
    state.value[type].selectedMerchantCategoryUuid = uuid;
  };

  const setAreAllMerchantCategoriesSelected = (type: MerchantType, value: boolean | null): void => {
    state.value[type].areAllMerchantCategoriesSelected = value;
  };

  const setExcludeSelectedMerchantCategoryUuids = (type: MerchantType, uuids: string[]): void => {
    state.value[type].excludeSelectedMerchantCategoryUuids = uuids;
  };

  const setLocalMerchantCategories = (type: MerchantType, categories: Merchant[]): void => {
    state.value[type].localMerchantCategories = categories;
  };

  const setSelectedMultipleMerchantCategories = (
    type: MerchantType,
    categories: Merchant[],
  ): void => {
    state.value[type].selectedMultipleMerchantCategories = categories;
  };

  const setSelectedSingleMerchantCategory = (type: MerchantType, categories: Merchant[]): void => {
    state.value[type].selectedSingleMerchantCategory = categories;
  };

  const setCategoriesCount = (type: MerchantType, count: number): void => {
    state.value[type].categoriesCount = count;
  };

  const setCategoriesTotal = (type: MerchantType, total: number): void => {
    state.value[type].categoriesTotal = total;
  };

  const setIsHideChoiceChips = (type: MerchantType, value: boolean): void => {
    state.value[type].isHideChoiceChips = value;
  };

  const setListChangedCount = (type: MerchantType, count: number): void => {
    state.value[type].listChangedCount = count;
  };

  const setIsFetching = (type: MerchantType, value: boolean): void => {
    state.value[type].isFetching = value;
  };

  // Complex actions
  const appendLocalMerchantCategories = (type: MerchantType, categories: Merchant[]): void => {
    state.value[type].localMerchantCategories = [
      ...state.value[type].localMerchantCategories,
      ...categories,
    ];
  };

  const resetLocalMerchantCategories = (type: MerchantType): void => {
    state.value[type].localMerchantCategories = [];
    state.value[type].listChangedCount += 1;
  };

  const removeSelectedMerchantCategoryUuid = (type: MerchantType, uuid: string): void => {
    const merchantState = state.value[type];

    // Remove from selected UUIDs
    merchantState.selectedMerchantCategoryUuids = merchantState
      .selectedMerchantCategoryUuids.filter(
        (selectedUuid) => selectedUuid !== uuid,
      );

    // Decrease count
    merchantState.categoriesCount -= 1;

    // Update selection state
    if (merchantState.areAllMerchantCategoriesSelected !== null
      && merchantState.areAllMerchantCategoriesSelected
      && merchantState.selectedMerchantCategoryUuids.length) {
      merchantState.areAllMerchantCategoriesSelected = null;
    } else if (!merchantState.selectedMerchantCategoryUuids.length
      && merchantState.areAllMerchantCategoriesSelected === null) {
      merchantState.areAllMerchantCategoriesSelected = false;
    }
  };

  const resetState = (type: MerchantType): void => {
    state.value[type] = createInitialMerchantCategoryState();
  };

  return {
    // Getters
    getMerchantCategoryLocksState,
    getMerchantLocksState,
    getStateByType,
    getIsRestrictionsModalVisible,
    getSelectedRestrictionOption,
    getIsCategoryModalVisible,
    getSearchText,
    getSelectedMerchantCategoryUuids,
    getSelectedMerchantCategoryUuid,
    getAreAllMerchantCategoriesSelected,
    getExcludeSelectedMerchantCategoryUuids,
    getLocalMerchantCategories,
    getSelectedMultipleMerchantCategories,
    getSelectedSingleMerchantCategory,
    getCategoriesCount,
    getCategoriesTotal,
    getIsHideChoiceChips,
    getListChangedCount,
    getIsFetching,
    getSelectedMerchantCategory,
    getIsSelectedLabelVisible,
    getAreAllCategoriesVisible,

    // Actions
    setIsRestrictionsModalVisible,
    setSelectedRestrictionOption,
    setIsCategoryModalVisible,
    setSearchText,
    setSelectedMerchantCategoryUuids,
    setSelectedMerchantCategoryUuid,
    setAreAllMerchantCategoriesSelected,
    setExcludeSelectedMerchantCategoryUuids,
    setLocalMerchantCategories,
    setSelectedMultipleMerchantCategories,
    setSelectedSingleMerchantCategory,
    setCategoriesCount,
    setCategoriesTotal,
    setIsHideChoiceChips,
    setListChangedCount,
    setIsFetching,
    appendLocalMerchantCategories,
    resetLocalMerchantCategories,
    removeSelectedMerchantCategoryUuid,
    resetState,
  };
});
