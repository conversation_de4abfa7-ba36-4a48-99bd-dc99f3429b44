<template>
  <div
    class="issue-card-merchant-locks"
    data-cy="issue-card-merchant-locks"
  >
    <CardMerchantCategoryRestrictions
      v-if="isVisibleByDefault || isMerchantCategoryLocksFieldVisible"
      v-bind="merchantCategoryProps"
      v-model="formRef.isLimitMerchantCategories"
      v-model:merchant-restriction-type="formRef.merchantCategoryRestrictionType"
      v-model:merchant-category-uuids="formRef.merchantCategoryUuids"
      class="q-mt-lg"
      :selected-merchant-category-option="formRef.selectedMerchantCategoryLocks"
      :is-master-card-flow="isMastercardMerchantCategoriesLocksVisible"
      :is-form-disabled="isMerchantCategoryDisabled"
      :are-all-selected="formRef.areAllMerchantCategorySelected"
      :merchant-categories-count="cardData?.card?.selectedMerchantCategoryLocks?.length"
      :merchant-categories-total="merchantCategoriesTotal"
      is-hide-chip-icon
      merchant-endpoint="merchant-categories"
      data-cy="issue-card-merchant-locks-categories"
      @update:is-master-card-flow="emit('update:is-master-card-flow', $event)"
      @update:exclude-uuids-requested="formRef.merchantCategoryExcludeUuids = $event"
      @update:are-all-items-selected="formRef.areAllMerchantCategorySelected = $event"
      @update:search-text="formRef.merchantCategorySearchText = $event"
      @update:selected-merchant-category-option="formRef.selectedMerchantCategoryLocks = $event"
    />

    <CardMerchantCategoryRestrictions
      v-if="isVisibleByDefault
        || (isMerchantLocksRevampSectionVisible && !isMerchantLockToggleDisabled)"
      v-bind="merchantLocksProps"
      v-model="formRef.isLimitMerchants"
      v-model:merchant-category-uuids="formRef.merchantUuids"
      v-model:merchant-restriction-type="formRef.merchantRestrictionType"
      class="q-mt-lg"
      :selected-merchant-category-option="formRef.selectedMerchantLocks"
      :is-master-card-flow="isMastercardMerchantLocksVisible"
      :is-form-disabled="isMerchantCategoryDisabled"
      :are-all-selected="formRef.areAllMerchantSelected"
      :merchant-categories-count="cardData?.card?.selectedMerchantLocks?.length"
      :merchant-categories-total="merchantLocksTotal"
      is-merchant-locks
      merchant-endpoint="merchant-locks"
      data-cy="issue-card-merchant-locks-merchants"
      @update:is-master-card-flow="emit('update:is-master-card-flow', $event)"
      @update:exclude-uuids-requested="formRef.merchantExcludeUuids = $event"
      @update:are-all-items-selected="formRef.areAllMerchantSelected = $event"
      @update:search-text="formRef.merchantSearchText = $event"
      @update:selected-merchant-category-option="formRef.selectedMerchantLocks = $event"
    />
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, ref, toRef } from 'vue';
import useCardMerchantCategory from 'features/card/common/card-details/merchant/composables/useCardMerchantCategory.ts';
import useCardMerchantCategoryProps from 'features/card/common/card-details/merchant/composables/useCardMerchantCategoryProps.ts';
import useCardApi from 'features/card/composables/useCardApi.ts';
import { type IssueCard, type IssueCardData } from 'features/card/issue-card/types/IssueCard.ts';

type Props = {
  isMasterCardFlow: boolean;
  isMerchantCategoryDisabled: boolean;
  form: IssueCard;
  cardData?: IssueCardData;
  isMerchantLockToggleDisabled?: boolean;
  isVisibleByDefault?: boolean;
};

type Emits = {
  'update:is-master-card-flow': [value: boolean];
};

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

/* eslint-disable @typescript-eslint/naming-convention */
const CardMerchantCategoryRestrictions = defineAsyncComponent(() => import('features/card/common/card-details/merchant/CardMerchantCategoryRestrictions.vue'));
/* eslint-enable @typescript-eslint/naming-convention */

const { fetchCardMerchantCategories } = useCardApi();
const { merchantCategoryProps, merchantLocksProps } = useCardMerchantCategory();
const {
  isMerchantLocksRevampSectionVisible,
  isMerchantCategoryLocksFieldVisible,
  isMastercardMerchantCategoriesLocksVisible,
  isMastercardMerchantLocksVisible,
} = useCardMerchantCategoryProps(toRef(props, 'isMasterCardFlow'));

const formRef = toRef(props, 'form');
const merchantCategoriesTotal = ref(0);
const merchantLocksTotal = ref(0);

const init = async (): Promise<void> => {
  if (!props.cardData?.card?.selectedMerchantLocks?.length
  || !props.cardData?.card?.selectedMerchantCategoryLocks?.length) {
    return;
  }

  const [merchantCategories, merchants] = await Promise.all([
    fetchCardMerchantCategories('merchant-categories', 1, undefined, undefined, ['uuid'], isMastercardMerchantCategoriesLocksVisible.value),
    fetchCardMerchantCategories('merchant-locks', 1, undefined, undefined, ['uuid'], isMastercardMerchantLocksVisible.value),
  ]);

  formRef.value.areAllMerchantCategorySelected = merchantCategories?.total
    === props.cardData?.card?.selectedMerchantCategoryLocks?.length;
  formRef.value.areAllMerchantSelected = merchants?.total
    === props.cardData?.card?.selectedMerchantLocks?.length;
  merchantCategoriesTotal.value = merchantCategories?.total ?? 0;
  merchantLocksTotal.value = merchants?.total ?? 0;
};

void init();
</script>
