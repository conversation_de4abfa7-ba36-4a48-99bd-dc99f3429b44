import { type IssueCard, type IssueCardData } from 'features/card/issue-card/types/IssueCard.ts';
import { type RequestCard, type RequestCardEdit } from 'features/card/request-card/types/RequestCard.ts';
import { type Merchant } from 'features/debit/types/Merchant.ts';

export enum CardMerchantCategoryRestriction {
  ALLOW = 'allowed',
  DENY = 'blocked',
}

export enum MerchantType {
  MERCHANT_CATEGORY_LOCKS = 'merchantCategoryLocks',
  MERCHANT_LOCKS = 'merchantLocks',
}

export type CardMerchantCategoryRestrictionOption = {
  name: string;
  description: string;
  value: CardMerchantCategoryRestriction | string;
};

export type MerchantCategoryState = {
  // Restriction modal state
  isRestrictionsModalVisible: boolean;
  selectedRestrictionOption: CardMerchantCategoryRestrictionOption | undefined;

  // Category modal state
  isCategoryModalVisible: boolean;
  searchText: string;

  // Selection state
  selectedMerchantCategoryUuids: string[];
  selectedMerchantCategoryUuid: string;
  areAllMerchantCategoriesSelected: boolean | null;
  excludeSelectedMerchantCategoryUuids: string[];

  // Data state
  localMerchantCategories: Merchant[];
  selectedMultipleMerchantCategories: Merchant[];
  selectedSingleMerchantCategory: Merchant[];

  // UI state
  categoriesCount: number;
  categoriesTotal: number;
  isHideChoiceChips: boolean;
  listChangedCount: number;
  isFetching: boolean;
};

export type CardMerchantCategoryStoreState = {
  merchantCategoryLocks: MerchantCategoryState;
  merchantLocks: MerchantCategoryState;
};

type CardMerchantCategoryRestrictionModelValueType = string | null;

export type CardMerchantCategoryProps = {
  // Essential props only - static configuration moved to store/composables
  modelValue?: boolean | null;
  merchantRestrictionType?: CardMerchantCategoryRestrictionModelValueType;
  merchantCategoryUuids?: Merchant['uuid'][];
  selectedMerchantCategoryOption?: Merchant[];
  isMasterCardFlow?: boolean;
  isFormDisabled?: boolean;
  isReadonlyMode?: boolean;
  merchantCategoriesCount?: number;
  isHideToggle?: boolean;
  merchantEndpoint: string;
  isMerchantLocks?: boolean;
  isHideChipIcon?: boolean;
  areAllSelected?: boolean | null;
  merchantCategoriesTotal?: number;
  isRequestCardEditFlow?: boolean;
  isCardEditFlow?: boolean;
};

// Static configuration type for internal use
export type CardMerchantCategoryConfig = {
  title: string;
  subTitle: string;
  tooltipContent: string;
  placeholderRestriction: string;
  selectedCategoryLabel: string;
  placeholderCategory: string;
  titleRestrictionModal: string;
  searchPlaceholder: string;
  selectAllLabel: string;
  titleSelectModal: string;
  subTitleSelectModal: string;
  confirmButtonLabel: string;
};

export type CardMerchantCategoryEmits = {
  'update:modelValue': [value: boolean];
  'update:merchant-restriction-type': [value: CardMerchantCategoryRestrictionModelValueType];
  'update:merchant-category-uuids': [value: string[]];
  'update:searchText': [value: string | null];
  'update:are-all-items-selected': [value: boolean | null];
  'update:exclude-uuids-requested': [value: Merchant['uuid'][]];
  'update:isMasterCardFlow': [value: boolean];
  'update:selected-merchant-category-option': [value: Merchant[]];
};

export type CardFormMerchantCategory = Pick<
  RequestCard & RequestCardEdit & IssueCard & IssueCardData,
  'areAllMerchantCategorySelected' | 'merchantCategoryRestrictionType' | 'merchantCategoryUuids' | 'merchantCategorySearchText' | 'merchantCategoryExcludeUuids' | 'isMasterCardFlow'
  | 'areAllMerchantSelected' | 'merchantRestrictionType' | 'merchantUuids' | 'merchantSearchText' | 'merchantExcludeUuids'
>;
