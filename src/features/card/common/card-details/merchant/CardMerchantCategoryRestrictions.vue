<template>
  <CardEditableDropdown
    :model-value="modelValue"
    :title="$t(title, Number(isMasterCardFlow))"
    :sub-title="isMasterCardFlow
      ? $t(subTitle)
      : ''"
    :tooltip-content="$t(tooltipContent, Number(isMasterCardFlow))"
    :is-form-disabled="isFormDisabled || isCategoryFormDisabledInEditFlow"
    :is-hide-toggle="isHideToggle"
    @update:model-value="emit('update:modelValue', $event)"
  >
    <template #list-select>
      <FieldSelectLabel
        :placeholder="$t(placeholderRestriction)"
        :is-selected-label-visible="!!selectedRestrictionOption"
        :is-disabled="isReadonlyMode || isCategoryFormDisabledInEditFlow"
        data-cy="card-merchant-category-restrictions-type-select-label"
        @clicked="isRestrictionsModalVisible = true"
      >
        <template #selected>
          <span
            class="text-form-field"
            data-cy="card-merchant-category-restrictions-selected-label"
          >
            {{ selectedRestrictionOption?.name }}
          </span>
        </template>
      </FieldSelectLabel>

      <FieldSelectLabel
        class="q-mt-md"
        :selected-label="
          !isMasterCardFlow && selectedMerchantCategoryUuids.length
            ? $t(selectedCategoryLabel, {
              count: areAllCategoriesVisible
                ? selectedMerchantCategoryUuids.length
                : categoriesCount,
            })
            : ''"
        :placeholder="$t(placeholderCategory, Number(isMasterCardFlow))"
        :is-selected-label-visible="isSelectedLabelVisible(isMasterCardFlow)"
        :is-disabled="isReadonlyMode
          || !selectedRestrictionOption
          || isCategoryFormDisabledInEditFlow"
        data-cy="card-merchant-category-restrictions-category-select-label"
        @clicked="isCategoryModalVisible = true"
      >
        <template
          v-if="isMasterCardFlow && selectedMerchantCategory"
          #selected
        >
          <div class="flex items-center">
            <template v-if="isMerchantLocks">
              <MintIcon
                v-if="selectedMerchantCategory.logo_url"
                class="card-merchant-category-restrictions__logo q-mr-sm"
                :icon="selectedMerchantCategory.logo_url || 'AspireLogoIcon'"
                :alt="selectedMerchantCategory.name"
                color="primary"
                aspire-theme="dark"
              />

              <MintIcon
                v-else
                class="opacity-50 q-mr-sm"
                icon="AspireLogoIcon"
                :size="16"
              />
            </template>

            <span
              class="text-form-field"
              data-cy="card-merchant-category-category-selected-label"
            >
              {{ selectedMerchantCategory.name }}
            </span>
          </div>
        </template>
      </FieldSelectLabel>

      <CardMerchantCategoryChoiceChips
        v-if="merchantCategoryChips?.length && !selectedMerchantCategory"
        class="q-mt-sm"
        :categories="merchantCategoryChips"
        :is-disabled="isReadonlyMode || isCategoryFormDisabledInEditFlow"
        :is-hide-icon="isHideChipIcon"
        @close-icon-clicked="handleRemoveSelectedMerchantCategoryUuid"
        @view-more-requested="isCategoryModalVisible = true"
      />

      <CardMerchantRestrictionsModal
        v-model="isRestrictionsModalVisible"
        :title="$t(titleRestrictionModal, Number(isMasterCardFlow))"
        :options="!isMerchantLocks
          ? merchantCategoryRestrictionOptions(!!isMasterCardFlow)
          : merchantRestrictionOptions(!!isMasterCardFlow)"
        :selected-restriction-option="selectedRestrictionOption"
        data-cy="card-merchant-category-restrictions-type-select-modal"
        @option-clicked="handleSelectRestrictionOption"
      />

      <template v-if="isCategoryModalVisible">
        <CardMultipleMerchantSelectModal
          v-if="!isMasterCardFlow"
          v-model="isCategoryModalVisible"
          v-model:search-text="searchText"
          v-model:are-all-items-selected="areAllMerchantCategoriesSelected"
          :categories="localMerchantCategories"
          :is-fetching="isFetching"
          :list-changed-count="listChangedCount"
          :selected-items="selectedMerchantCategoryUuids"
          :search-placeholder="$t(searchPlaceholder)"
          :select-all-label="$t(selectAllLabel)"
          :title="$t(titleSelectModal, 2)"
          :categories-count="categoriesCount"
          :are-all-items-visible="areAllCategoriesVisible"
          :exclude-item-uuids="excludeSelectedMerchantCategoryUuids"
          :categories-total="categoriesTotal"
          :is-merchant-lock="isMerchantLocks"
          :selected-item-options="selectedMerchantCategoryOption"
          :initial-list="initialMerchantCategories"
          @load-more-requested="onHandleFetchCardMerchantCategories"
          @confirm-requested="onConfirmMerchantCategories"
          @update:search-text="onSearchChange"
          @update:are-all-items-selected="onUpdateCheckAll"
          @exclude-uuids-requested="onExcludeUuidsRequested"
          @modal-closed="localMerchantCategories = []"
          @update-count-requested="categoriesCount = $event"
          @hide-chips-requested="isHideChoiceChips = $event"
          @update:selected-items="onSelectMultipleOptions"
        />

        <CardSingleMerchantSelectModal
          v-else
          v-model="isCategoryModalVisible"
          v-model:search-text="searchText"
          :list-changed-count="listChangedCount"
          :categories="localMerchantCategories"
          :selected-uuid="selectedMerchantCategoryUuid"
          :sub-title="$t(subTitleSelectModal)"
          :search-placeholder="$t(searchPlaceholder)"
          :confirm-button-label="$t(confirmButtonLabel)"
          :title="$t(titleSelectModal, 1)"
          :is-merchant-lock="isMerchantLocks"
          @load-more-requested="onHandleFetchCardMerchantCategories"
          @confirm-requested="onConfirmMerchantCategories"
          @update:search-text="onSearchChange"
          @modal-closed="localMerchantCategories = []"
          @update:selected-items="onSelectSingleOption"
        />
      </template>
    </template>

    <template
      #after
    >
      <MintBanner
        v-if="isWarningBannerVisible"
        class="q-mt-md"
        :title="!isErrorBannerVisible && !isMerchantLocks
          ? $t('debit-card-item.merchant-category-restriction.warning-note.title')
          : ''"
        :content="warningBannerMessage"
        type="warning"
        data-cy="card-merchant-category-restrictions-warning-banner"
      />

      <slot name="after-list" />
    </template>
  </CardEditableDropdown>
</template>

<script setup lang="ts">
import { isEqual } from 'lodash-es';
import MintBanner from 'quasar-app-extension-customer-frontend-mint-ui/src/components/banner/MintBanner.vue';
import {
  computed, defineAsyncComponent, onMounted, ref, watch,
} from 'vue';
import useCardMerchantCategory from 'features/card/common/card-details/merchant/composables/useCardMerchantCategory.ts';
import useCardMerchantCategoryList from 'features/card/common/card-details/merchant/composables/useCardMerchantCategoryList.ts';
import CardMerchantCategoryChoiceChips from 'features/card/common/card-details/merchant/CardMerchantCategoryChoiceChips.vue';
import CardMerchantRestrictionsModal from 'features/card/common/card-details/merchant/CardMerchantRestrictionsModal.vue';
import CardEditableDropdown from 'features/card/common/fields/CardEditableDropdown.vue';
import FieldSelectLabel from 'features/card/common/fields/FieldSelectLabel.vue';
import {
  type CardMerchantCategoryEmits,
  type CardMerchantCategoryProps,
  type CardMerchantCategoryRestrictionOption,
} from 'features/card/types/CardMerchantCategory.ts';
import { type Merchant } from 'features/debit/types/Merchant.ts';
import { type QuasarInfiniteScrollLoadFunction } from 'src/types/UI.ts';

defineSlots<{
  'after-list': [];
}>();

// eslint-disable-next-line vue/no-unused-properties
const props = defineProps<CardMerchantCategoryProps>();

const emit = defineEmits<CardMerchantCategoryEmits>();

/* eslint-disable @typescript-eslint/naming-convention */
const CardMultipleMerchantSelectModal = defineAsyncComponent(() => import('features/card/common/card-details/merchant/CardMultipleMerchantSelectModal.vue'));
const CardSingleMerchantSelectModal = defineAsyncComponent(() => import('features/card/common/card-details/merchant/CardSingleMerchantSelectModal.vue'));
/* eslint-enable @typescript-eslint/naming-convention */

const {
  merchantCategoryRestrictionOptions,
  isCategoryModalVisible, isRestrictionsModalVisible,
  searchText, selectedRestrictionOption, merchantRestrictionOptions,
  onSelectRestrictionOption, prefillMerchantRestrictionType,
} = useCardMerchantCategory();
const {
  isFetching, localMerchantCategories, listChangedCount, warningBannerMessage,
  categoriesTotal, isHideChoiceChips, isErrorBannerVisible, selectedSingleMerchantCategory,
  selectedMerchantCategoryUuid, excludeSelectedMerchantCategoryUuids,
  selectedMerchantCategoryUuids, isSelectedLabelVisible, selectedMerchantCategory,
  areAllMerchantCategoriesSelected, categoriesCount, shouldShowWarningBanner,
  handleFetchCardMerchantCategories, resetCardMerchantCategories,
  removeSelectedMerchantCategoryUuid, fetchTotalMerchantCategories,
  setInitialTotalMerchantCategories, prefillMerchantCategoryUuids,
  prefillCategoriesCount,
} = useCardMerchantCategoryList(props);

// Should not change this value to keep the original value in preview mode
// User can edit the value in preview mode
// `initialMerchantCategories` will help to compare the difference between the edited value
// and original value
const initialMerchantCategories = ref<Merchant[]>(props.selectedMerchantCategoryOption ?? []);
const selectedMultipleMerchantCategories = ref<Merchant[]>(
  props.isMasterCardFlow
    ? []
    : props.selectedMerchantCategoryOption ?? [],
);

const areAllCategoriesVisible = computed(
  () => areAllMerchantCategoriesSelected.value !== false
    && initialMerchantCategories.value.length === categoriesTotal.value,
);

const isWarningBannerVisible = computed(() => shouldShowWarningBanner.value(props.isMasterCardFlow)
  && warningBannerMessage.value.length);

const merchantCategoryChips = computed(() => {
  if (isHideChoiceChips.value) {
    return [];
  }

  return selectedMultipleMerchantCategories.value;
});

const isCategoryFormDisabledInEditFlow = computed(
  () => !props.isMerchantLocks
  && props.isRequestCardEditFlow,
);

const emitMerchantCategoryUuids = (
  uuids: Merchant['uuid'][],
  isMasterCardFlow: boolean,
): void => {
  if (isMasterCardFlow) {
    const [firstUUid] = uuids;

    if (firstUUid) {
      selectedMerchantCategoryUuid.value = firstUUid;
    }
  } else {
    selectedMerchantCategoryUuids.value = uuids;
  }

  emit('update:merchant-category-uuids', uuids);
};

const onConfirmMerchantCategories = (uuids: Merchant['uuid'][]): void => {
  emitMerchantCategoryUuids(uuids, props.isMasterCardFlow);

  localMerchantCategories.value = [];
  isCategoryModalVisible.value = false;
};

const handleSelectRestrictionOption = (option: CardMerchantCategoryRestrictionOption): void => {
  onSelectRestrictionOption(option);
  emit('update:merchant-restriction-type', option.value);
};

const onSearchChange = async (value: string | null): Promise<void> => {
  emit('update:searchText', value);

  await fetchTotalMerchantCategories(
    props.merchantEndpoint,
    searchText.value,
    props.isMasterCardFlow,
  );

  resetCardMerchantCategories();
};

const onHandleFetchCardMerchantCategories: QuasarInfiniteScrollLoadFunction = async (
  page,
  done,
) => {
  await handleFetchCardMerchantCategories(
    props.merchantEndpoint,
    page,
    done,
    searchText.value,
    props.isMasterCardFlow,
  );
};

const onExcludeUuidsRequested = (uuids: Merchant['uuid'][]): void => {
  excludeSelectedMerchantCategoryUuids.value = uuids;
  emit('update:exclude-uuids-requested', uuids);
};

const onUpdateCheckAll = (value: boolean | null): void => {
  if (value === null || value) {
    emit('update:are-all-items-selected', true);

    return;
  }

  emit('update:are-all-items-selected', false);
};

const handleRemoveSelectedMerchantCategoryUuid = (uuid: Merchant['uuid']): void => {
  selectedMultipleMerchantCategories.value = selectedMultipleMerchantCategories.value.filter(
    (item) => item.uuid !== uuid,
  );

  if (areAllMerchantCategoriesSelected.value === null
  || areAllMerchantCategoriesSelected.value) {
    excludeSelectedMerchantCategoryUuids.value = [
      ...excludeSelectedMerchantCategoryUuids.value,
      uuid,
    ];
    emit('update:exclude-uuids-requested', excludeSelectedMerchantCategoryUuids.value);
  }

  removeSelectedMerchantCategoryUuid(uuid);
  emit('update:selected-merchant-category-option', selectedMultipleMerchantCategories.value);
};

const onSelectMultipleOptions = (options: Merchant[]): void => {
  selectedMultipleMerchantCategories.value = options;
  emit('update:selected-merchant-category-option', options);
};

const onSelectSingleOption = (options: Merchant[]): void => {
  selectedSingleMerchantCategory.value = options;
  emit('update:selected-merchant-category-option', options);
};

watch(() => props.merchantCategoriesCount, (value) => {
  if (value !== undefined) {
    prefillCategoriesCount(value);
  }
}, { immediate: true });

watch(() => props.merchantRestrictionType, () => {
  prefillMerchantRestrictionType(
    props.isMasterCardFlow,
    props.merchantRestrictionType ?? '',
    props.isMerchantLocks,
  );
}, { immediate: true });

watch(() => props.merchantCategoryUuids, (newValue) => {
  prefillMerchantCategoryUuids(
    newValue,
    props.isMasterCardFlow,
  );
}, { immediate: true });

watch(selectedMerchantCategoryUuids, (newValue, oldValue) => {
  emit('update:merchant-category-uuids', newValue);

  // Reset selected uuid when choose IDR first then switch to SGD and choose multiple categories
  if (!props.isReadonlyMode
  && selectedMerchantCategory.value
  && !props.isMasterCardFlow
  && !isEqual(newValue, oldValue)) {
    selectedMerchantCategoryUuid.value = '';
  }
});

watch(() => props.isMasterCardFlow, (newValue) => {
  if (!newValue
  && selectedMerchantCategoryUuid.value
  && props.selectedMerchantCategoryOption?.length
  && !selectedMerchantCategoryUuids.value.length) {
    // Reset selected merchant category uuids if choose IDR first
    emit('update:selected-merchant-category-option', []);
  }

  if (selectedMerchantCategoryUuids.value.length
      && selectedMerchantCategory.value) {
    selectedMerchantCategoryUuid.value = '';
    selectedSingleMerchantCategory.value = [];
    selectedMerchantCategoryUuids.value = [];
    selectedMultipleMerchantCategories.value = [];
  }

  if (newValue) {
    emit('update:merchant-category-uuids', [selectedMerchantCategoryUuid.value]);
    emit('update:selected-merchant-category-option', selectedSingleMerchantCategory.value);
  } else {
    emit('update:merchant-category-uuids', selectedMerchantCategoryUuids.value);
    emit('update:selected-merchant-category-option', selectedMultipleMerchantCategories.value);
  }

  emit('update:isMasterCardFlow', newValue);
});

watch(() => props.areAllSelected, (newValue) => {
  areAllMerchantCategoriesSelected.value = !!newValue;
}, { immediate: true });

watch(() => props.merchantCategoriesTotal, (newValue) => {
  categoriesTotal.value = newValue ?? 0;
}, { immediate: true });

onMounted(() => {
  void setInitialTotalMerchantCategories(
    props.merchantEndpoint,
    props.merchantCategoriesCount ?? 0,
    searchText.value,
    props.isMasterCardFlow,
  );
});
</script>

<style lang="scss" scoped>
.card-merchant-category-restrictions__logo {
  width: 1rem;
  height: 1rem;
  object-fit: contain;
}
</style>
