<template>
  <CardEditableDropdown
    :model-value="modelValue"
    :title="$t(config.title, Number(isMasterCardFlow))"
    :sub-title="isMasterCardFlow
      ? $t(config.subTitle)
      : ''"
    :tooltip-content="$t(config.tooltipContent, Number(isMasterCardFlow))"
    :is-form-disabled="isFormDisabled || isCategoryFormDisabledInEditFlow"
    :is-hide-toggle="isHideToggle"
    @update:model-value="emit('update:modelValue', $event)"
  >
    <template #list-select>
      <FieldSelectLabel
        :placeholder="$t(config.placeholderRestriction)"
        :is-selected-label-visible="!!selectedRestrictionOption"
        :is-disabled="isReadonlyMode || isCategoryFormDisabledInEditFlow"
        data-cy="card-merchant-category-restrictions-type-select-label"
        @clicked="isRestrictionsModalVisible = true"
      >
        <template #selected>
          <span
            class="text-form-field"
            data-cy="card-merchant-category-restrictions-selected-label"
          >
            {{ selectedRestrictionOption?.name }}
          </span>
        </template>
      </FieldSelectLabel>

      <FieldSelectLabel
        class="q-mt-md"
        :selected-label="
          !isMasterCardFlow && selectedMerchantCategoryUuids.length
            ? $t(config.selectedCategoryLabel, {
              count: areAllCategoriesVisible
                ? selectedMerchantCategoryUuids.length
                : categoriesCount,
            })
            : ''"
        :placeholder="$t(config.placeholderCategory, Number(isMasterCardFlow))"
        :is-selected-label-visible="isSelectedLabelVisible(isMasterCardFlow)"
        :is-disabled="isReadonlyMode
          || !selectedRestrictionOption
          || isCategoryFormDisabledInEditFlow"
        data-cy="card-merchant-category-restrictions-category-select-label"
        @clicked="isCategoryModalVisible = true"
      >
        <template
          v-if="isMasterCardFlow && selectedMerchantCategory"
          #selected
        >
          <div class="flex items-center">
            <template v-if="isMerchantLocks">
              <MintIcon
                v-if="selectedMerchantCategory.logo_url"
                class="card-merchant-category-restrictions__logo q-mr-sm"
                :icon="selectedMerchantCategory.logo_url || 'AspireLogoIcon'"
                :alt="selectedMerchantCategory.name"
                color="primary"
                aspire-theme="dark"
              />

              <MintIcon
                v-else
                class="opacity-50 q-mr-sm"
                icon="AspireLogoIcon"
                :size="16"
              />
            </template>

            <span
              class="text-form-field"
              data-cy="card-merchant-category-category-selected-label"
            >
              {{ selectedMerchantCategory.name }}
            </span>
          </div>
        </template>
      </FieldSelectLabel>

      <CardMerchantCategoryChoiceChips
        v-if="merchantCategoryChips?.length && !selectedMerchantCategory"
        class="q-mt-sm"
        :categories="merchantCategoryChips"
        :is-disabled="isReadonlyMode || isCategoryFormDisabledInEditFlow"
        :is-hide-icon="isHideChipIcon"
        @close-icon-clicked="handleRemoveSelectedMerchantCategoryUuid"
        @view-more-requested="isCategoryModalVisible = true"
      />

      <CardMerchantRestrictionsModal
        v-model="isRestrictionsModalVisible"
        :title="$t(config.titleRestrictionModal, Number(isMasterCardFlow))"
        :options="configComposable.getRestrictionOptions.value(merchantType, !!isMasterCardFlow)"
        :selected-restriction-option="selectedRestrictionOption"
        data-cy="card-merchant-category-restrictions-type-select-modal"
        @option-clicked="handleSelectRestrictionOption"
      />

      <template v-if="isCategoryModalVisible">
        <CardMultipleMerchantSelectModal
          v-if="!isMasterCardFlow"
          v-model="isCategoryModalVisible"
          :title="$t(config.titleSelectModal, 2)"
          :search-placeholder="$t(config.searchPlaceholder)"
          :select-all-label="$t(config.selectAllLabel)"
          :confirm-button-label="$t(config.confirmButtonLabel)"
          :is-merchant-lock="isMerchantLocks"
          @load-more-requested="onHandleFetchCardMerchantCategories"
          @confirm-requested="onConfirmMerchantCategories"
          @modal-closed="storeComposable.setLocalMerchantCategories(merchantType, [])"
        />

        <CardSingleMerchantSelectModal
          v-else
          v-model="isCategoryModalVisible"
          :sub-title="$t(config.subTitleSelectModal)"
          :search-placeholder="$t(config.searchPlaceholder)"
          :confirm-button-label="$t(config.confirmButtonLabel)"
          :title="$t(config.titleSelectModal, 1)"
          :is-merchant-lock="isMerchantLocks"
          @load-more-requested="onHandleFetchCardMerchantCategories"
          @confirm-requested="onConfirmMerchantCategories"
          @modal-closed="storeComposable.setLocalMerchantCategories(merchantType, [])"
        />
      </template>
    </template>

    <template
      #after
    >
      <MintBanner
        v-if="isWarningBannerVisible"
        class="q-mt-md"
        :title="!isMerchantLocks
          ? $t('debit-card-item.merchant-category-restriction.warning-note.title')
          : ''"
        :content="warningBannerMessage"
        type="warning"
        data-cy="card-merchant-category-restrictions-warning-banner"
      />

      <slot name="after-list" />
    </template>
  </CardEditableDropdown>
</template>

<script setup lang="ts">
import { isEqual } from 'lodash-es';
import MintBanner from 'quasar-app-extension-customer-frontend-mint-ui/src/components/banner/MintBanner.vue';
import {
  computed, defineAsyncComponent, onMounted, watch,
} from 'vue';
import useCardMerchantCategoryConfig from 'features/card/common/card-details/merchant/composables/useCardMerchantCategoryConfig.ts';
import useCardMerchantCategoryStore from 'features/card/common/card-details/merchant/composables/useCardMerchantCategoryStore.ts';
import useMerchantCategoryApi from 'features/card/common/card-details/merchant/composables/useMerchantCategoryApi.ts';
import {
  emitMerchantCategoryUuids,
  handleMasterCardFlowChange,
  prefillMerchantCategoryUuids,
} from 'features/card/common/card-details/merchant/utils/merchantCategoryUtils.ts';
import CardMerchantCategoryChoiceChips from 'features/card/common/card-details/merchant/CardMerchantCategoryChoiceChips.vue';
import CardMerchantRestrictionsModal from 'features/card/common/card-details/merchant/CardMerchantRestrictionsModal.vue';
import CardEditableDropdown from 'features/card/common/fields/CardEditableDropdown.vue';
import FieldSelectLabel from 'features/card/common/fields/FieldSelectLabel.vue';
import {
  type CardMerchantCategoryEmits,
  type CardMerchantCategoryProps,
  type CardMerchantCategoryRestrictionOption,
} from 'features/card/types/CardMerchantCategory.ts';
import { type Merchant } from 'features/debit/types/Merchant.ts';
import { type QuasarInfiniteScrollLoadFunction } from 'src/types/UI.ts';

defineSlots<{
  'after-list': [];
}>();

// eslint-disable-next-line vue/no-unused-properties
const props = defineProps<CardMerchantCategoryProps>();

const emit = defineEmits<CardMerchantCategoryEmits>();

/* eslint-disable @typescript-eslint/naming-convention */
const CardMultipleMerchantSelectModal = defineAsyncComponent(() => import('features/card/common/card-details/merchant/CardMultipleMerchantSelectModal.vue'));
const CardSingleMerchantSelectModal = defineAsyncComponent(() => import('features/card/common/card-details/merchant/CardSingleMerchantSelectModal.vue'));
/* eslint-enable @typescript-eslint/naming-convention */

// Initialize composables
const storeComposable = useCardMerchantCategoryStore();
const configComposable = useCardMerchantCategoryConfig();
const apiComposable = useMerchantCategoryApi();

// Get merchant type based on props
const merchantType = storeComposable.getMerchantTypeFromProps(props.isMerchantLocks);

// Get configuration for this merchant type
const config = configComposable.getConfigByType.value(merchantType);

// Computed properties using store
const selectedRestrictionOption = computed(
  () => storeComposable.getSelectedRestrictionOption(merchantType),
);

const isRestrictionsModalVisible = computed({
  get: () => storeComposable.getIsRestrictionsModalVisible(merchantType),
  set: (value: boolean) => storeComposable.setIsRestrictionsModalVisible(merchantType, value),
});

const isCategoryModalVisible = computed({
  get: () => storeComposable.getIsCategoryModalVisible(merchantType),
  set: (value: boolean) => storeComposable.setIsCategoryModalVisible(merchantType, value),
});

const selectedMerchantCategoryUuids = computed(
  () => storeComposable.getSelectedMerchantCategoryUuids(merchantType),
);

const selectedMerchantCategory = computed(
  () => storeComposable.getSelectedMerchantCategory(merchantType),
);

const categoriesCount = computed(() => storeComposable.getCategoriesCount(merchantType));

const areAllCategoriesVisible = computed(
  () => storeComposable.getAreAllCategoriesVisible(merchantType),
);

const merchantCategoryChips = computed(
  () => storeComposable.getMerchantCategoryChips(merchantType),
);

const warningBannerMessage = computed(() => storeComposable.getWarningBannerMessage(
  merchantType,
  !!props.isMasterCardFlow,
  !!props.isMerchantLocks,
  !!props.isRequestCardEditFlow,
  !!props.isCardEditFlow,
));

const isWarningBannerVisible = computed(
  () => storeComposable.getShouldShowWarningBanner(!!props.isMasterCardFlow)
  && warningBannerMessage.value.length,
);

const isSelectedLabelVisible = computed(
  () => (isMasterCardFlow?: boolean): boolean => storeComposable.getIsSelectedLabelVisible(
    merchantType,
    isMasterCardFlow,
  ),
);

const isCategoryFormDisabledInEditFlow = computed(
  () => !props.isMerchantLocks && props.isRequestCardEditFlow,
);

// Action functions using store and utilities
const handleSelectRestrictionOption = (option: CardMerchantCategoryRestrictionOption): void => {
  storeComposable.setSelectedRestrictionOption(merchantType, option);
  storeComposable.setIsRestrictionsModalVisible(merchantType, false);
  emit('update:merchant-restriction-type', option.value);
};

const onConfirmMerchantCategories = (uuids: Merchant['uuid'][]): void => {
  emitMerchantCategoryUuids(
    uuids,
    !!props.isMasterCardFlow,
    (uuid: string) => storeComposable.setSelectedMerchantCategoryUuid(merchantType, uuid),
    (merchantUuids: string[]) => storeComposable.setSelectedMerchantCategoryUuids(
      merchantType,
      merchantUuids,
    ),
  );

  storeComposable.setLocalMerchantCategories(merchantType, []);
  storeComposable.setIsCategoryModalVisible(merchantType, false);
  emit('update:merchant-category-uuids', uuids);
};

const onHandleFetchCardMerchantCategories: QuasarInfiniteScrollLoadFunction = async (
  page,
  done,
) => {
  await apiComposable.handleFetchCardMerchantCategories(
    merchantType,
    props.merchantEndpoint,
    page,
    done,
    storeComposable.getSearchText(merchantType),
    props.isMasterCardFlow,
  );
};

const handleRemoveSelectedMerchantCategoryUuid = (uuid: Merchant['uuid']): void => {
  const currentMultiple = storeComposable.getSelectedMultipleMerchantCategories(merchantType);
  const filteredCategories = currentMultiple.filter((item) => item.uuid !== uuid);

  storeComposable.setSelectedMultipleMerchantCategories(merchantType, filteredCategories);

  const areAllSelected = storeComposable.getAreAllMerchantCategoriesSelected(merchantType);

  if (areAllSelected === null || areAllSelected) {
    const currentExcluded = storeComposable.getExcludeSelectedMerchantCategoryUuids(merchantType);
    const newExcluded = [...currentExcluded, uuid];

    storeComposable.setExcludeSelectedMerchantCategoryUuids(merchantType, newExcluded);
    emit('update:exclude-uuids-requested', newExcluded);
  }

  storeComposable.removeSelectedMerchantCategoryUuid(merchantType, uuid);
  emit('update:selected-merchant-category-option', filteredCategories);
};

// Watchers for props changes
watch(() => props.merchantCategoriesCount, (value) => {
  if (value !== undefined) {
    storeComposable.setCategoriesCount(merchantType, value);
  }
}, { immediate: true });

watch(() => props.merchantRestrictionType, (restrictionType) => {
  if (restrictionType) {
    const option = configComposable.findRestrictionOption(
      merchantType,
      !!props.isMasterCardFlow,
      restrictionType,
    );

    storeComposable.setSelectedRestrictionOption(merchantType, option);
  }
}, { immediate: true });

watch(() => props.merchantCategoryUuids, (newValue) => {
  if (newValue) {
    prefillMerchantCategoryUuids(
      newValue,
      !!props.isMasterCardFlow,
      (uuid: string) => storeComposable.setSelectedMerchantCategoryUuid(merchantType, uuid),
      (uuids: string[]) => storeComposable.setSelectedMerchantCategoryUuids(merchantType, uuids),
    );
  }
}, { immediate: true });

watch(selectedMerchantCategoryUuids, (newValue, oldValue) => {
  emit('update:merchant-category-uuids', newValue);

  // Reset selected uuid when choose IDR first then switch to SGD and choose multiple categories
  if (!props.isReadonlyMode
  && selectedMerchantCategory.value
  && !props.isMasterCardFlow
  && !isEqual(newValue, oldValue)) {
    storeComposable.setSelectedMerchantCategoryUuid(merchantType, '');
  }
});

watch(() => props.isMasterCardFlow, (newValue) => {
  const currentSelectedUuid = storeComposable.getSelectedMerchantCategoryUuid(merchantType);
  const currentSelectedUuids = storeComposable.getSelectedMerchantCategoryUuids(merchantType);
  const currentSelectedCategory = storeComposable.getSelectedMerchantCategory(merchantType);
  const currentSingleCategories = storeComposable.getSelectedSingleMerchantCategory(merchantType);
  const currentMultipleCategories = storeComposable.getSelectedMultipleMerchantCategories(
    merchantType,
  );

  const result = handleMasterCardFlowChange(
    !!newValue,
    currentSelectedUuid,
    props.selectedMerchantCategoryOption,
    currentSelectedUuids,
    currentSelectedCategory,
    {
      setSelectedMerchantCategoryUuid: (
        uuid: string,
      ) => storeComposable.setSelectedMerchantCategoryUuid(merchantType, uuid),
      setSelectedSingleMerchantCategory: (
        categories: Merchant[],
      ) => storeComposable.setSelectedSingleMerchantCategory(merchantType, categories),
      setSelectedMerchantCategoryUuids: (
        uuids: string[],
      ) => storeComposable.setSelectedMerchantCategoryUuids(merchantType, uuids),
      setSelectedMultipleMerchantCategories: (
        categories: Merchant[],
      ) => storeComposable.setSelectedMultipleMerchantCategories(merchantType, categories),
    },
  );

  emit('update:merchant-category-uuids', result.uuids);
  emit('update:selected-merchant-category-option', newValue
    ? currentSingleCategories
    : currentMultipleCategories);
  emit('update:isMasterCardFlow', newValue);
});

watch(() => props.areAllSelected, (newValue) => {
  storeComposable.setAreAllMerchantCategoriesSelected(merchantType, !!newValue);
}, { immediate: true });

watch(() => props.merchantCategoriesTotal, (newValue) => {
  storeComposable.setCategoriesTotal(merchantType, newValue ?? 0);
}, { immediate: true });

onMounted(() => {
  void apiComposable.setInitialTotalMerchantCategories(
    merchantType,
    props.merchantEndpoint,
    props.merchantCategoriesCount ?? 0,
    storeComposable.getSearchText(merchantType),
    props.isMasterCardFlow,
  );
});
</script>

<style lang="scss" scoped>
.card-merchant-category-restrictions__logo {
  width: 1rem;
  height: 1rem;
  object-fit: contain;
}
</style>
