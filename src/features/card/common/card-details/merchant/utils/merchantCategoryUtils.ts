import useUtility from 'src/composables/useUtility.ts';
import { MerchantType } from 'features/card/types/CardMerchantCategory.ts';
import { type Merchant } from 'features/debit/types/Merchant.ts';

const { t } = useUtility();

export const shouldShowWarningBanner = (isMasterCardFlow?: boolean): boolean => !!isMasterCardFlow;

export const generateWarningBannerMessage = (
  isMasterCardFlow: boolean,
  isMerchantLocks: boolean,
  isRequestCardEditFlow: boolean,
  isCardEditFlow: boolean,
  hasSelectedMerchantCategory: boolean,
  hasSelectedMerchantCategoryUuids: boolean,
): string => {
  const isErrorBannerVisible = shouldShowWarningBanner(isMasterCardFlow)
    && !hasSelectedMerchantCategory
    && hasSelectedMerchantCategoryUuids;

  if (isErrorBannerVisible) {
    return isMerchantLocks
      ? t('debit-card-item.merchant-restriction.error-banner')
      : t('debit-card-item.merchant-category-restriction.error-banner');
  }

  if (isRequestCardEditFlow || isCardEditFlow) {
    return isMerchantLocks
      ? ''
      : t('debit-card-item.merchant-category-restriction.mastercard-disabled.message');
  }

  return isMerchantLocks
    ? ''
    : t('debit-card-item.merchant-category-restriction.warning-note.message');
};

export const isSelectedLabelVisible = (
  isMasterCardFlow: boolean,
  selectedMerchantCategoryUuid: string,
  selectedMerchantCategoryUuids: string[],
): boolean => {
  if (isMasterCardFlow) {
    return !!selectedMerchantCategoryUuid;
  }

  return !!selectedMerchantCategoryUuids.length;
};

export const findSelectedMerchantCategory = (
  selectedMerchantCategoryUuid: string,
  selectedSingleMerchantCategory: Merchant[],
): Merchant | undefined => {
  if (!selectedMerchantCategoryUuid) {
    return undefined;
  }

  return selectedSingleMerchantCategory.find(
    (category) => category.uuid === selectedMerchantCategoryUuid,
  );
};

export const areAllCategoriesVisible = (
  areAllMerchantCategoriesSelected: boolean | null,
  selectedMultipleMerchantCategories: Merchant[],
  categoriesTotal: number,
): boolean => areAllMerchantCategoriesSelected !== false
    && selectedMultipleMerchantCategories.length === categoriesTotal;

export const filterMerchantCategoryChips = (
  isHideChoiceChips: boolean,
  selectedMultipleMerchantCategories: Merchant[],
): Merchant[] => {
  if (isHideChoiceChips) {
    return [];
  }

  return selectedMultipleMerchantCategories;
};

export const emitMerchantCategoryUuids = (
  uuids: string[],
  isMasterCardFlow: boolean,
  setSelectedMerchantCategoryUuid: (uuid: string) => void,
  setSelectedMerchantCategoryUuids: (uuids: string[]) => void,
): void => {
  if (isMasterCardFlow) {
    const [firstUuid] = uuids;

    if (firstUuid) {
      setSelectedMerchantCategoryUuid(firstUuid);
    }
  } else {
    setSelectedMerchantCategoryUuids(uuids);
  }
};

export const prefillMerchantCategoryUuids = (
  merchantCategoryUuids: string[] | undefined,
  isMasterCardFlow: boolean,
  setSelectedMerchantCategoryUuid: (uuid: string) => void,
  setSelectedMerchantCategoryUuids: (uuids: string[]) => void,
): void => {
  if (!merchantCategoryUuids) {
    return;
  }

  if (isMasterCardFlow) {
    const [firstUuid] = merchantCategoryUuids;

    setSelectedMerchantCategoryUuid(firstUuid ?? '');
  } else {
    setSelectedMerchantCategoryUuids(merchantCategoryUuids);
  }
};

export const updateSelectionStateOnRemove = (
  uuid: string,
  areAllMerchantCategoriesSelected: boolean | null,
  selectedMerchantCategoryUuids: string[],
  setAreAllMerchantCategoriesSelected: (value: boolean | null) => void,
): void => {
  const remainingUuids = selectedMerchantCategoryUuids.filter(
    (selectedUuid) => selectedUuid !== uuid,
  );

  if (areAllMerchantCategoriesSelected !== null
    && areAllMerchantCategoriesSelected
    && remainingUuids.length) {
    setAreAllMerchantCategoriesSelected(null);
  } else if (!remainingUuids.length
    && areAllMerchantCategoriesSelected === null) {
    setAreAllMerchantCategoriesSelected(false);
  }
};

export const handleMasterCardFlowChange = (
  newValue: boolean,
  selectedMerchantCategoryUuid: string,
  selectedMerchantCategoryOption: Merchant[] | undefined,
  selectedMerchantCategoryUuids: string[],
  selectedMerchantCategory: Merchant | undefined,
  setters: {
    setSelectedMerchantCategoryUuid: (uuid: string) => void;
    setSelectedSingleMerchantCategory: (categories: Merchant[]) => void;
    setSelectedMerchantCategoryUuids: (uuids: string[]) => void;
    setSelectedMultipleMerchantCategories: (categories: Merchant[]) => void;
  },
): { uuids: string[]; options: Merchant[] } => {
  const {
    setSelectedMerchantCategoryUuid,
    setSelectedSingleMerchantCategory,
    setSelectedMerchantCategoryUuids,
    setSelectedMultipleMerchantCategories,
  } = setters;

  if (!newValue
    && selectedMerchantCategoryUuid
    && selectedMerchantCategoryOption?.length
    && !selectedMerchantCategoryUuids.length) {
    // Reset selected merchant category uuids if choose IDR first
    return { uuids: [], options: [] };
  }

  if (selectedMerchantCategoryUuids.length && selectedMerchantCategory) {
    setSelectedMerchantCategoryUuid('');
    setSelectedSingleMerchantCategory([]);
    setSelectedMerchantCategoryUuids([]);
    setSelectedMultipleMerchantCategories([]);
  }

  if (newValue) {
    return {
      uuids: [selectedMerchantCategoryUuid],
      options: [], // selectedSingleMerchantCategory will be set by caller
    };
  }

  return {
    uuids: selectedMerchantCategoryUuids,
    options: [], // selectedMultipleMerchantCategories will be set by caller
  };
};

export const validateMerchantCategorySelection = (
  selectedUuids: string[],
  isMasterCardFlow: boolean,
): boolean => {
  if (isMasterCardFlow) {
    return selectedUuids.length === 1;
  }

  return selectedUuids.length > 0;
};

export const getMerchantTypeFromProps = (
  isMerchantLocks?: boolean,
): MerchantType => (isMerchantLocks
  ? MerchantType.MERCHANT_LOCKS
  : MerchantType.MERCHANT_CATEGORY_LOCKS);
