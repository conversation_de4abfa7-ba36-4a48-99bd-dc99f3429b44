<template>
  <CardCategoriesBottomSheet
    v-model="model"
    v-model:search-text="searchText"
    :confirm-button-label="confirmButtonLabel"
    :search-placeholder="searchPlaceholder"
    :title="title"
    :list-changed-count="listChangedCount"
    :sub-title="subTitle"
    @confirm-requested="onConfirmSelectedItem"
    @load-more-requested="emitLoadMore"
    @modal-closed="emit('modal-closed')"
  >
    <template v-if="!isMerchantLock">
      <div
        v-for="category in filteredCategories"
        :key="category.uuid"
        class="card-single-merchant-select-modal__item"
        data-cy="card-single-merchant-select-modal-item"
      >
        <MintRadio
          v-model="selectedCategory"
          :label="category.name"
          :val="category.uuid"
          data-cy="card-single-merchant-select-modal-item-checkbox"
        />
      </div>
    </template>

    <template v-else>
      <q-item
        v-for="merchant in filteredCategories"
        :key="merchant.uuid"
        v-ripple
        class="card-single-merchant-select-modal__item"
        tag="label"
        data-cy="card-single-merchant-select-modal-item"
      >
        <q-item-section>
          <q-item-label class="row no-wrap items-center">
            <MintRadio
              v-model="selectedCategory"
              :val="merchant.uuid"
              data-cy="card-single-merchant-select-modal-item-checkbox"
            />

            <q-card class="inline-block q-ml-sm q-mr-md">
              <div
                class="card-single-merchant-select-modal__logo
              row justify-center items-center"
              >
                <img
                  v-if="merchant.logo_url"
                  :src="merchant.logo_url"
                  :alt="merchant.name"
                >

                <MintIcon
                  v-else
                  class="opacity-50"
                  icon="AspireLogoIcon"
                  :size="20"
                />
              </div>
            </q-card>

            <span class="text-body1-emphasis text-secondary">{{ merchant.name }}</span>
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </CardCategoriesBottomSheet>
</template>

<script setup lang="ts">
import { type QInfiniteScrollProps } from 'quasar';
import MintRadio from 'quasar-app-extension-customer-frontend-mint-ui/src/components/radio/MintRadio.vue';
import { computed } from 'vue';
import useCardMerchantCategoryStore from 'features/card/common/card-details/merchant/composables/useCardMerchantCategoryStore.ts';
import useIssueCardFormValidation from 'features/card/issue-card/composables/useIssueCardFormValidation.ts';
import CardCategoriesBottomSheet from 'features/card/common/card-details/CardCategoriesBottomSheet.vue';
import { type Merchant } from 'features/debit/types/Merchant.ts';

type Props = {
  // Essential props that cannot be derived from store
  isMerchantLock?: boolean;
  // Configuration props - these will be obtained from config composable internally
  subTitle: string;
  title: string;
  searchPlaceholder: string;
  confirmButtonLabel: string;
};

type Emits = {
  'confirm-requested': [value: Merchant['uuid'][]];
  'load-more-requested': [page: number, done: (value?: boolean) => void];
  'modal-closed': [];
};

const model = defineModel<boolean>({
  required: true,
});

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { showListSelectionError } = useIssueCardFormValidation();

// Initialize composables
const storeComposable = useCardMerchantCategoryStore();

// Get merchant type based on props
const merchantType = storeComposable.getMerchantTypeFromProps(props.isMerchantLock);

// Store-based reactive state
const searchText = computed({
  get: () => storeComposable.getSearchText(merchantType),
  set: (value: string) => {
    storeComposable.setSearchText(merchantType, value);
    emit('update:search-text', value);
  },
});

const selectedCategory = computed({
  get: () => storeComposable.getSelectedMerchantCategoryUuid(merchantType),
  set: (value: string) => storeComposable.setSelectedMerchantCategoryUuid(merchantType, value),
});

const categories = computed(() => storeComposable.getLocalMerchantCategories(merchantType));

const listChangedCount = computed(() => storeComposable.getListChangedCount(merchantType));

const filteredCategories = computed(
  () => [...new Map(categories.value.map((item) => [item.uuid, item])).values()],
);

const emitLoadMore: QInfiniteScrollProps['onLoad'] = (page, done) => {
  emit('load-more-requested', page, done);
};

const onConfirmSelectedItem = (): void => {
  if (!selectedCategory.value) {
    showListSelectionError({
      errorMessage: 'debit-cards.merchant-category-restriction-selection.empty',
      isMultipleSelection: false,
    });

    return;
  }

  // Update store with selected category
  const selectedCategories = categories.value.filter(
    (category) => category.uuid === selectedCategory.value,
  );

  storeComposable.setSelectedSingleMerchantCategory(merchantType, selectedCategories);

  emit('confirm-requested', [selectedCategory.value]);
};
</script>

<style lang="scss" scoped>
.card-single-merchant-select-modal__logo {
  width: 2rem;
  height: 2rem;
  border-radius: .25rem;

  img {
    width: 1.25rem;
    height: 1.25rem;
    object-fit: contain;
  }
}

.card-single-merchant-select-modal__item {
  padding-left: 0;
}
</style>
