<template>
  <CardCategoriesBottomSheet
    v-model="model"
    :confirm-button-label="buttonLabel"
    :title="title"
    :select-all-label="selectAllLabel"
    :search-placeholder="searchPlaceholder"
    :search-text="searchText"
    :is-fetching="isFetching"
    :list-changed-count="listChangedCount"
    data-cy="card-multiple-merchant-select-modal"
    @confirm-requested="onConfirmAdd"
    @update:search-text="handleSearchTextChange"
    @load-more-requested="emitLoadMore"
    @modal-closed="emit('modal-closed')"
  >
    <template #after-title>
      <MintButton
        :class="{
          'card-multiple-merchant-select-modal__clear-button--disable opacity-50':
            !selectedCategories.length,
        }"
        :label="$t('common.clear-selection')"
        flat
        data-cy="card-multiple-merchant-select-modal-clear-button"
        @clicked="onClearMerchantCategories"
      />
    </template>

    <template #after-search>
      <MintCheckbox
        :model-value="areAllItemsSelectedLocal"
        class="card-multiple-merchant-select-modal__select-all-checkbox color-info"
        :label="selectAllLabel"
        data-cy="card-multiple-merchant-select-modal-select-all-button"
        @update:model-value="onSelectAllMerchantCategories(!!$event)"
      />
    </template>

    <template v-if="!isMerchantLock">
      <div
        v-for="category in initialCategoriesList"
        :key="category.uuid"
        class="card-multiple-merchant-select-modal__item"
      >
        <MintCheckbox
          :model-value="selectedCategories.includes(category.uuid)"
          :label="category.name"
          data-cy="card-multiple-merchant-select-modal-checkbox"
          @update:model-value="(value: boolean) => updateCheckedCheckboxes(category.uuid, value)"
        />
      </div>
    </template>

    <template v-else>
      <q-item
        v-for="merchant in initialCategoriesList"
        :key="merchant.uuid"
        v-ripple
        class="card-multiple-merchant-select-modal__item"
        tag="label"
      >
        <q-item-section>
          <q-item-label class="row no-wrap items-center">
            <MintCheckbox
              :model-value="selectedCategories.includes(merchant.uuid)"
              data-cy="card-multiple-merchant-select-modal-checkbox"
              @update:model-value="updateCheckedCheckboxes(merchant.uuid, $event)"
            />

            <q-card class="inline-block q-ml-sm q-mr-md">
              <div
                class="card-multiple-merchant-select-modal__logo row justify-center items-center"
              >
                <img
                  v-if="merchant.logo_url"
                  :src="merchant.logo_url"
                  :alt="merchant.name"
                >

                <MintIcon
                  v-else
                  class="opacity-50"
                  icon="AspireLogoIcon"
                  :size="20"
                />
              </div>
            </q-card>

            <span class="text-body1-emphasis text-secondary">{{ merchant.name }}</span>
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </CardCategoriesBottomSheet>
</template>

<script setup lang="ts">
import MintCheckbox from 'quasar-app-extension-customer-frontend-mint-ui/src/components/checkbox/MintCheckbox.vue';
import { computed, ref, watch } from 'vue';
import useUtility from 'src/composables/useUtility.ts';
import useCardMerchantCategoryConfig from 'features/card/common/card-details/merchant/composables/useCardMerchantCategoryConfig.ts';
import useCardMerchantCategoryStore from 'features/card/common/card-details/merchant/composables/useCardMerchantCategoryStore.ts';
import useIssueCardFormValidation from 'features/card/issue-card/composables/useIssueCardFormValidation.ts';
import CardCategoriesBottomSheet from 'features/card/common/card-details/CardCategoriesBottomSheet.vue';
import { type Merchant } from 'features/debit/types/Merchant.ts';
import { type QuasarInfiniteScrollLoadFunction } from 'src/types/UI.ts';

type Props = {
  // Essential props that cannot be derived from store
  isMerchantLock?: boolean;
  // Configuration props - these will be obtained from config composable internally
  title: string;
  searchPlaceholder: string;
  selectAllLabel: string;
  confirmButtonLabel?: string;
};

type Emits = {
  'confirm-requested': [value: Merchant['uuid'][]];
  'load-more-requested': [page: number, done: (value?: boolean) => void];
  'modal-closed': [];
};

const model = defineModel<boolean>({
  required: true,
});

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t } = useUtility();
const { showListSelectionError } = useIssueCardFormValidation();

// Initialize composables
const storeComposable = useCardMerchantCategoryStore();
const configComposable = useCardMerchantCategoryConfig();

// Get merchant type based on props
const merchantType = storeComposable.getMerchantTypeFromProps(props.isMerchantLock);

// Store-based reactive state
const searchText = computed({
  get: () => storeComposable.getSearchText(merchantType),
  set: (value: string) => {
    storeComposable.setSearchText(merchantType, value);
    emit('update:search-text', value);
  },
});

const countSelectedCategories = computed(() => storeComposable.getCategoriesCount(merchantType));

const selectedCategories = computed(
  () => storeComposable.getSelectedMerchantCategoryUuids(merchantType),
);

const areAllItemsSelectedLocal = computed(
  () => storeComposable.getAreAllMerchantCategoriesSelected(merchantType),
);

const excludeSelectedMerchantCategoryUuids = computed(
  () => storeComposable.getExcludeSelectedMerchantCategoryUuids(merchantType),
);

const localCategories = computed(() => storeComposable.getLocalMerchantCategories(merchantType));

const categoriesTotal = computed(() => storeComposable.getCategoriesTotal(merchantType));

const isFetching = computed(() => storeComposable.getIsFetching(merchantType));

const areAllItemsVisible = computed(() => storeComposable.getAreAllCategoriesVisible(merchantType));

const listChangedCount = computed(() => storeComposable.getListChangedCount(merchantType));

// Local state for search functionality
const searchedMerchantCategories = ref<Merchant[]>([]);

const isIndeterminateState = computed(
  () => areAllItemsSelectedLocal.value === null
  || areAllItemsSelectedLocal.value,
);

const initialCategoriesList = computed(
  () => [...new Map([
    ...localCategories.value,
    ...searchedMerchantCategories.value,
  ].map((category) => [category.uuid, category])).values()],
);

const selectedLocalCategories = computed(
  () => initialCategoriesList.value
    .filter(
      ({ uuid }) => selectedCategories.value.includes(uuid),
    ),
);

const allSelectedMerchantCategories = computed(
  () => [...new Map([
    ...selectedLocalCategories.value,
    ...searchedMerchantCategories.value,
  ].map((currency) => [currency.uuid, currency])).values()],
);

// When the user has selected all categories, but not all categories are visible
const areAllCategoriesNotVisible = computed(
  () => categoriesTotal.value !== allSelectedMerchantCategories.value.length
  && areAllItemsSelectedLocal.value !== false
  && !areAllItemsVisible.value,
);

const buttonTranslationKey = computed(() => (!props.isMerchantLock
  ? 'debit-card-item.merchant-category-restriction.select-modal-save-cta'
  : 'debit-card-item.merchant-restriction.select-modal-save-cta'));

const buttonDisableTranslationKey = computed(() => (!props.isMerchantLock
  ? 'debit-card-item.merchant-category-restriction.select-modal-disable-cta-disable'
  : 'debit-card-item.merchant-restriction.select-modal-disable-cta-disable'));

const buttonLabel = computed(
  () => {
    if (props.confirmButtonLabel) {
      return props.confirmButtonLabel;
    }

    return !selectedCategories.value.length
      ? t(buttonDisableTranslationKey.value)
      : t(
        buttonTranslationKey.value,
        {
          count: countSelectedCategories.value && isIndeterminateState.value
            ? countSelectedCategories.value
            : selectedCategories.value.length,
        },
        selectedCategories.value.length,
      );
  },
);

const categoryUuids = computed(() => initialCategoriesList.value.map(({ uuid }) => uuid));

const onSelectAllMerchantCategories = (value: boolean): void => {
  if (areAllItemsSelectedLocal.value === null) {
    storeComposable.setSelectedMerchantCategoryUuids(merchantType, []);
    storeComposable.setExcludeSelectedMerchantCategoryUuids(merchantType, []);
    storeComposable.setAreAllMerchantCategoriesSelected(merchantType, false);

    return;
  }

  storeComposable.setAreAllMerchantCategoriesSelected(merchantType, value);

  const newSelectedUuids = value
    ? categoryUuids.value
    : [];

  storeComposable.setSelectedMerchantCategoryUuids(merchantType, newSelectedUuids);

  storeComposable.setCategoriesCount(merchantType, categoriesTotal.value);

  if (!value) {
    storeComposable.setExcludeSelectedMerchantCategoryUuids(merchantType, []);
  }
};

const onClearMerchantCategories = (): void => {
  storeComposable.setSelectedMerchantCategoryUuids(merchantType, []);
  storeComposable.setAreAllMerchantCategoriesSelected(merchantType, false);
  storeComposable.setExcludeSelectedMerchantCategoryUuids(merchantType, []);
};

const updateSearchedCurrencies = (uuid: string, isChecked: boolean): void => {
  if (!searchText.value) {
    return;
  }

  if (isChecked) {
    const currencies = localCategories.value.filter(
      ({ uuid: categoryUuid }) => uuid === categoryUuid,
    );

    searchedMerchantCategories.value = [
      ...searchedMerchantCategories.value,
      ...currencies,
    ];
  } else {
    searchedMerchantCategories.value = searchedMerchantCategories.value.filter(
      ({ uuid: categoryUuid }) => categoryUuid !== uuid,
    );
  }
};

const updateSelectedMerchantCategories = (uuid: string, isChecked: boolean): void => {
  const currentSelected = selectedCategories.value;

  if (isChecked) {
    const newSelected = [...currentSelected, uuid];

    storeComposable.setSelectedMerchantCategoryUuids(merchantType, newSelected);
  } else {
    const newSelected = currentSelected.filter((id) => id !== uuid);

    storeComposable.setSelectedMerchantCategoryUuids(merchantType, newSelected);
  }
};

const updateSelectedMerchantCategoriesCount = (
  uuid: string,
  isChecked: boolean,
): void => {
  const currentCount = countSelectedCategories.value;
  const currentExcluded = excludeSelectedMerchantCategoryUuids.value;

  if (isChecked) {
    storeComposable.setCategoriesCount(merchantType, currentCount + 1);

    const newExcluded = currentExcluded.filter((id) => id !== uuid);

    storeComposable.setExcludeSelectedMerchantCategoryUuids(merchantType, newExcluded);
  } else {
    storeComposable.setCategoriesCount(merchantType, currentCount - 1);

    const newExcluded = [...currentExcluded, uuid];

    storeComposable.setExcludeSelectedMerchantCategoryUuids(merchantType, newExcluded);
  }
};

const updateCheckedCheckboxes = (uuid: string, isChecked: boolean): void => {
  updateSelectedMerchantCategories(uuid, isChecked);
  updateSelectedMerchantCategoriesCount(uuid, isChecked);
  updateSearchedCurrencies(uuid, isChecked);
};

const emitLoadMore: QuasarInfiniteScrollLoadFunction = (page, done) => {
  emit('load-more-requested', page, done);
};

const onConfirmAdd = (): void => {
  if (!selectedCategories.value.length) {
    showListSelectionError({
      errorMessage: 'debit-cards.merchant-category-restriction-selection.empty',
      isMultipleSelection: true,
    });

    return;
  }

  // Update store state
  storeComposable.setAreAllMerchantCategoriesSelected(merchantType, areAllItemsSelectedLocal.value);

  if (areAllItemsSelectedLocal.value) {
    storeComposable.setExcludeSelectedMerchantCategoryUuids(merchantType, []);
  }

  if (areAllItemsSelectedLocal.value === null) {
    storeComposable.setExcludeSelectedMerchantCategoryUuids(
      merchantType,
      excludeSelectedMerchantCategoryUuids.value,
    );
  }

  const finalCount = isIndeterminateState.value
    ? countSelectedCategories.value
    : selectedCategories.value.length;

  storeComposable.setCategoriesCount(merchantType, finalCount);

  storeComposable.setIsHideChoiceChips(merchantType, areAllCategoriesNotVisible.value);
  storeComposable.setSelectedMultipleMerchantCategories(
    merchantType,
    allSelectedMerchantCategories.value,
  );

  // Only emit the essential event
  emit('confirm-requested', selectedCategories.value);
};

const handleSearchTextChange = (value?: string | null): void => {
  if (isIndeterminateState.value) {
    storeComposable.setSelectedMerchantCategoryUuids(merchantType, []);
    storeComposable.setExcludeSelectedMerchantCategoryUuids(merchantType, []);
    storeComposable.setAreAllMerchantCategoriesSelected(merchantType, false);
  }

  if (value) {
    storeComposable.setLocalMerchantCategories(merchantType, []);
  } else {
    // Reset to initial categories when search is cleared
    const selectedMultiple = storeComposable.getSelectedMultipleMerchantCategories(merchantType);
    const localCats = storeComposable.getLocalMerchantCategories(merchantType);

    storeComposable.setLocalMerchantCategories(merchantType, [...selectedMultiple, ...localCats]);
  }

  searchText.value = value ?? '';
};

// Watchers for store state changes
watch(selectedCategories, (newValue) => {
  if (newValue.length
        && newValue.length !== initialCategoriesList.value.length
        && areAllItemsSelectedLocal.value) {
    storeComposable.setAreAllMerchantCategoriesSelected(merchantType, null);
  } else if (
    newValue.length === initialCategoriesList.value.length
      && areAllItemsSelectedLocal.value === null
  ) {
    storeComposable.setAreAllMerchantCategoriesSelected(merchantType, true);
  }
});

watch(localCategories, (newValue) => {
  if (areAllItemsSelectedLocal.value === false
    && !areAllCategoriesNotVisible.value
  ) {
    return;
  }

  const currentExcluded = excludeSelectedMerchantCategoryUuids.value;
  const currentSelected = selectedCategories.value;

  const newUuids = newValue
    .filter(({ uuid }) => !currentExcluded.includes(uuid))
    .map(({ uuid }) => uuid);

  const updatedSelected = [...new Set([...currentSelected, ...newUuids])];

  storeComposable.setSelectedMerchantCategoryUuids(merchantType, updatedSelected);
});
</script>

<style lang="scss" scoped>
.card-multiple-merchant-select-modal__select-all-checkbox :deep(.q-checkbox__label) {
  font-weight: 600!important; // Override the default MintCheckbox font-weight
}

.card-multiple-merchant-select-modal__clear-button--disable {
  pointer-events: none;
}

.card-multiple-merchant-select-modal__logo {
  width: 2rem;
  height: 2rem;
  border-radius: .25rem;

  img {
    width: 1.25rem;
    height: 1.25rem;
    object-fit: contain;
  }
}

.card-multiple-merchant-select-modal__item {
  padding-left: 0;
}
</style>
