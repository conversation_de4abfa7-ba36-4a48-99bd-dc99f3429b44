<template>
  <CardCategoriesBottomSheet
    v-model="model"
    :confirm-button-label="buttonLabel"
    :title="title"
    :select-all-label="selectAllLabel"
    :search-placeholder="searchPlaceholder"
    :search-text="searchText"
    :is-fetching="isFetching"
    :list-changed-count="listChangedCount"
    data-cy="card-multiple-merchant-select-modal"
    @confirm-requested="onConfirmAdd"
    @update:search-text="handleSearchTextChange"
    @load-more-requested="emitLoadMore"
    @modal-closed="emit('modal-closed')"
  >
    <template #after-title>
      <MintButton
        :class="{
          'card-multiple-merchant-select-modal__clear-button--disable opacity-50':
            !selectedCategories.length,
        }"
        :label="$t('common.clear-selection')"
        flat
        data-cy="card-multiple-merchant-select-modal-clear-button"
        @clicked="onClearMerchantCategories"
      />
    </template>

    <template #after-search>
      <MintCheckbox
        :model-value="areAllItemsSelectedLocal"
        class="card-multiple-merchant-select-modal__select-all-checkbox color-info"
        :label="selectAllLabel"
        data-cy="card-multiple-merchant-select-modal-select-all-button"
        @update:model-value="onSelectAllMerchantCategories(!!$event)"
      />
    </template>

    <template v-if="!isMerchantLock">
      <div
        v-for="category in initialCategoriesList"
        :key="category.uuid"
        class="card-multiple-merchant-select-modal__item"
      >
        <MintCheckbox
          :model-value="selectedCategories.includes(category.uuid)"
          :label="category.name"
          data-cy="card-multiple-merchant-select-modal-checkbox"
          @update:model-value="(value: boolean) => updateCheckedCheckboxes(category.uuid, value)"
        />
      </div>
    </template>

    <template v-else>
      <q-item
        v-for="merchant in initialCategoriesList"
        :key="merchant.uuid"
        v-ripple
        class="card-multiple-merchant-select-modal__item"
        tag="label"
      >
        <q-item-section>
          <q-item-label class="row no-wrap items-center">
            <MintCheckbox
              :model-value="selectedCategories.includes(merchant.uuid)"
              data-cy="card-multiple-merchant-select-modal-checkbox"
              @update:model-value="updateCheckedCheckboxes(merchant.uuid, $event)"
            />

            <q-card class="inline-block q-ml-sm q-mr-md">
              <div
                class="card-multiple-merchant-select-modal__logo row justify-center items-center"
              >
                <img
                  v-if="merchant.logo_url"
                  :src="merchant.logo_url"
                  :alt="merchant.name"
                >

                <MintIcon
                  v-else
                  class="opacity-50"
                  icon="AspireLogoIcon"
                  :size="20"
                />
              </div>
            </q-card>

            <span class="text-body1-emphasis text-secondary">{{ merchant.name }}</span>
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </CardCategoriesBottomSheet>
</template>

<script setup lang="ts">
import MintCheckbox from 'quasar-app-extension-customer-frontend-mint-ui/src/components/checkbox/MintCheckbox.vue';
import { computed, ref, watch } from 'vue';
import useUtility from 'src/composables/useUtility.ts';
import useIssueCardFormValidation from 'features/card/issue-card/composables/useIssueCardFormValidation.ts';
import CardCategoriesBottomSheet from 'features/card/common/card-details/CardCategoriesBottomSheet.vue';
import { type Merchant } from 'features/debit/types/Merchant.ts';
import { type QuasarInfiniteScrollLoadFunction } from 'src/types/UI.ts';

type Props = {
  title: string;
  searchPlaceholder: string;
  areAllItemsSelected: boolean | null;
  selectAllLabel: string;
  listChangedCount: number;
  selectedItems: Merchant['uuid'][];
  excludeItemUuids: Merchant['uuid'][];
  categories: Merchant[];
  confirmButtonLabel?: string;
  isFetching?: boolean;
  categoriesCount?: number;
  categoriesTotal?: number;
  areAllItemsVisible?: boolean;
  isMerchantLock?: boolean;
  selectedItemOptions?: Merchant[];
  initialList?: Merchant[];
};

type Emits = {
  'confirm-requested': [value: Merchant['uuid'][]];
  'update:are-all-items-selected': [value: boolean | null];
  'load-more-requested': [page: number, done: (value?: boolean) => void];
  'exclude-uuids-requested': [value: Merchant['uuid'][]];
  'modal-closed': [];
  'update-count-requested': [value: number];
  'hide-chips-requested': [value: boolean];
  'update:selected-items': [value: Merchant[]];
};

const model = defineModel<boolean>({
  required: true,
});
const searchText = defineModel<string | null>('searchText', {
  required: true,
});

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const { t } = useUtility();
const { showListSelectionError } = useIssueCardFormValidation();

const countSelectedCategories = ref(props.categoriesCount ?? 0);
const selectedCategories = ref<Merchant['uuid'][]>(props.selectedItems ?? []);
// The areAllItemsSelectedLocal has three values: null, true, false
// Null means that the user has not interacted with the select all checkbox
// True means that the user has selected all items
// False means that the user has not selected all items
const areAllItemsSelectedLocal = ref<boolean | null>(props.areAllItemsSelected);
const excludeSelectedMerchantCategoryUuids = ref<Merchant['uuid'][]>(props.excludeItemUuids);
const localCategories = ref<Merchant[]>([
  ...(props.initialList ?? []),
  ...(props.selectedItemOptions ?? []),
  ...props.categories,
]);
// checked currency that only visible after search
const searchedMerchantCategories = ref<Merchant[]>([]);

const isIndeterminateState = computed(
  () => areAllItemsSelectedLocal.value === null
  || areAllItemsSelectedLocal.value,
);

const initialCategoriesList = computed(
  () => [...new Map([
    ...localCategories.value,
    ...searchedMerchantCategories.value,
  ].map((category) => [category.uuid, category])).values()],
);

const selectedLocalCategories = computed(
  () => initialCategoriesList.value
    .filter(
      ({ uuid }) => selectedCategories.value.includes(uuid),
    ),
);

const allSelectedMerchantCategories = computed(
  () => [...new Map([
    ...selectedLocalCategories.value,
    ...searchedMerchantCategories.value,
  ].map((currency) => [currency.uuid, currency])).values()],
);

// When the user has selected all categories, but not all categories are visible
const areAllCategoriesNotVisible = computed(
  () => props.categoriesTotal !== allSelectedMerchantCategories.value.length
  && areAllItemsSelectedLocal.value !== false
  && !props.areAllItemsVisible,
);

const buttonTranslationKey = computed(() => (!props.isMerchantLock
  ? 'debit-card-item.merchant-category-restriction.select-modal-save-cta'
  : 'debit-card-item.merchant-restriction.select-modal-save-cta'));

const buttonDisableTranslationKey = computed(() => (!props.isMerchantLock
  ? 'debit-card-item.merchant-category-restriction.select-modal-disable-cta-disable'
  : 'debit-card-item.merchant-restriction.select-modal-disable-cta-disable'));

const buttonLabel = computed(
  () => {
    if (props.confirmButtonLabel) {
      return props.confirmButtonLabel;
    }

    return !selectedCategories.value.length
      ? t(buttonDisableTranslationKey.value)
      : t(
        buttonTranslationKey.value,
        {
          count: countSelectedCategories.value && isIndeterminateState.value
            ? countSelectedCategories.value
            : selectedCategories.value.length,
        },
        selectedCategories.value.length,
      );
  },
);

const categoryUuids = computed(() => initialCategoriesList.value.map(({ uuid }) => uuid));

const onSelectAllMerchantCategories = (value: boolean): void => {
  if (areAllItemsSelectedLocal.value === null) {
    selectedCategories.value = [];
    excludeSelectedMerchantCategoryUuids.value = [];
    areAllItemsSelectedLocal.value = false;

    return;
  }

  areAllItemsSelectedLocal.value = value;

  selectedCategories.value = value
    ? categoryUuids.value
    : [];

  countSelectedCategories.value = props.categoriesTotal ?? 0;

  if (!areAllItemsSelectedLocal.value) {
    excludeSelectedMerchantCategoryUuids.value = [];
  }
};

const onClearMerchantCategories = (): void => {
  selectedCategories.value = [];
  areAllItemsSelectedLocal.value = false;
  excludeSelectedMerchantCategoryUuids.value = [];
};

const updateSearchedCurrencies = (uuid: string, isChecked: boolean): void => {
  if (!searchText.value) {
    return;
  }

  if (isChecked) {
    const currencies = localCategories.value.filter(
      ({ uuid: categoryUuid }) => uuid === categoryUuid,
    );

    searchedMerchantCategories.value = [
      ...searchedMerchantCategories.value,
      ...currencies,
    ];
  } else {
    searchedMerchantCategories.value = searchedMerchantCategories.value.filter(
      ({ uuid: categoryUuid }) => categoryUuid !== uuid,
    );
  }
};

const updateSelectedMerchantCategories = (uuid: string, isChecked: boolean): void => {
  if (isChecked) {
    selectedCategories.value = [
      ...selectedCategories.value,
      uuid,
    ];
  } else {
    selectedCategories.value = selectedCategories.value.filter((id) => id !== uuid);
  }
};

const updateSelectedMerchantCategoriesCount = (
  uuid: string,
  isChecked: boolean,
): void => {
  if (isChecked) {
    countSelectedCategories.value += 1;
    excludeSelectedMerchantCategoryUuids.value = excludeSelectedMerchantCategoryUuids.value.filter(
      (id) => id !== uuid,
    );
  } else {
    countSelectedCategories.value -= 1;
    excludeSelectedMerchantCategoryUuids.value = [
      ...excludeSelectedMerchantCategoryUuids.value,
      uuid,
    ];
  }
};

const updateCheckedCheckboxes = (uuid: string, isChecked: boolean): void => {
  updateSelectedMerchantCategories(uuid, isChecked);
  updateSelectedMerchantCategoriesCount(uuid, isChecked);
  updateSearchedCurrencies(uuid, isChecked);
};

const emitLoadMore: QuasarInfiniteScrollLoadFunction = (page, done) => {
  emit('load-more-requested', page, done);
};

const onConfirmAdd = (): void => {
  if (!selectedCategories.value.length) {
    showListSelectionError({
      errorMessage: 'debit-cards.merchant-category-restriction-selection.empty',
      isMultipleSelection: true,
    });

    return;
  }

  emit('confirm-requested', selectedCategories.value);
  emit('update:are-all-items-selected', areAllItemsSelectedLocal.value);

  if (areAllItemsSelectedLocal.value) {
    emit('exclude-uuids-requested', []);
  }

  if (areAllItemsSelectedLocal.value === null) {
    emit('exclude-uuids-requested', excludeSelectedMerchantCategoryUuids.value);
  }

  emit('update-count-requested', isIndeterminateState.value
    ? countSelectedCategories.value
    : selectedCategories.value.length);

  emit('hide-chips-requested', areAllCategoriesNotVisible.value);
  emit('update:selected-items', allSelectedMerchantCategories.value);
};

const handleSearchTextChange = (value?: string | null): void => {
  if (isIndeterminateState.value) {
    selectedCategories.value = [];
    excludeSelectedMerchantCategoryUuids.value = [];
    areAllItemsSelectedLocal.value = false;
  }

  if (value) {
    localCategories.value = [];
  } else {
    localCategories.value = [
      ...(props.initialList ?? []),
      ...(props.selectedItemOptions ?? []),
      ...props.categories,
    ];
  }

  searchText.value = value ?? null;
};

const init = (): void => {
  if (props.areAllItemsSelected
  && !props.areAllItemsVisible) {
    countSelectedCategories.value = props.categoriesTotal ?? 0;
  }
};

watch(selectedCategories, (newValue) => {
  if (newValue.length
        && newValue.length !== initialCategoriesList.value.length
        && areAllItemsSelectedLocal.value) {
    areAllItemsSelectedLocal.value = null;
  } else if (
    newValue.length === initialCategoriesList.value.length
      && areAllItemsSelectedLocal.value === null
  ) {
    areAllItemsSelectedLocal.value = true;
  }
});

watch(() => props.categories, (newValue) => {
  localCategories.value = [
    ...localCategories.value,
    ...newValue,
  ];

  if (areAllItemsSelectedLocal.value === false
    && !areAllCategoriesNotVisible.value
  ) {
    return;
  }

  const newUuids = newValue
    .filter(
      ({ uuid }) => !excludeSelectedMerchantCategoryUuids.value.includes(uuid),
    )
    .map(({ uuid }) => uuid);

  selectedCategories.value = [...new Set([...props.selectedItems, ...newUuids])];
});

init();
</script>

<style lang="scss" scoped>
.card-multiple-merchant-select-modal__select-all-checkbox :deep(.q-checkbox__label) {
  font-weight: 600!important; // Override the default MintCheckbox font-weight
}

.card-multiple-merchant-select-modal__clear-button--disable {
  pointer-events: none;
}

.card-multiple-merchant-select-modal__logo {
  width: 2rem;
  height: 2rem;
  border-radius: .25rem;

  img {
    width: 1.25rem;
    height: 1.25rem;
    object-fit: contain;
  }
}

.card-multiple-merchant-select-modal__item {
  padding-left: 0;
}
</style>
