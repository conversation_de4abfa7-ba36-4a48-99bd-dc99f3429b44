import axios, { type CancelTokenSource } from 'axios';
import { computed, ref } from 'vue';
import useUtility from 'src/composables/useUtility.ts';
import useCardApi from 'features/card/composables/useCardApi.ts';
import {
  type CardMerchantCategoryProps,
} from 'features/card/types/CardMerchantCategory.ts';
import { type Merchant } from 'features/debit/types/Merchant.ts';

const useCardMerchantCategoryList = (props: CardMerchantCategoryProps) => {
  const { t } = useUtility();
  const { fetchCardMerchantCategories } = useCardApi();

  const isFetching = ref(false);
  const cancelToken = ref<CancelTokenSource>();
  const totalCancelToken = ref<CancelTokenSource>();
  const localMerchantCategories = ref<Merchant[]>(props.selectedMerchantCategoryOption ?? []);
  const isListEmpty = ref(false);
  const listChangedCount = ref(0);
  const areAllMerchantCategoriesSelected = ref<boolean | null>(false);
  const selectedMerchantCategoryUuids = ref<Merchant['uuid'][]>([]);
  const selectedMerchantCategoryUuid = ref('');
  const categoriesCount = ref(0);
  const categoriesTotal = ref(0);
  const allCardMerchantCategories = ref<Merchant[]>([]);
  const excludeSelectedMerchantCategoryUuids = ref<Merchant['uuid'][]>([]);
  const isHideChoiceChips = ref(false);

  const selectedSingleMerchantCategory = ref<Merchant[]>(
    !props.isMasterCardFlow
      ? []
      : props.selectedMerchantCategoryOption ?? [],
  );

  const selectedMerchantCategory = computed(() => {
    if (!selectedMerchantCategoryUuid.value) {
      return undefined;
    }

    return selectedSingleMerchantCategory.value.find(
      (category) => category.uuid === selectedMerchantCategoryUuid.value,
    );
  });

  const shouldShowWarningBanner = computed(
    () => (isMasterCardFlow?: boolean) => isMasterCardFlow,
  );

  const isErrorBannerVisible = computed(() => shouldShowWarningBanner.value(props.isMasterCardFlow)
    && !selectedMerchantCategory.value && selectedMerchantCategoryUuids.value.length);

  const warningBannerMessage = computed(() => {
    if (isErrorBannerVisible.value) {
      return props.isMerchantLocks
        ? t('debit-card-item.merchant-restriction.error-banner')
        : t('debit-card-item.merchant-category-restriction.error-banner');
    }

    if (!!props.isRequestCardEditFlow || !!props.isCardEditFlow) {
      return props.isMerchantLocks
        ? ''
        : t('debit-card-item.merchant-category-restriction.mastercard-disabled.message');
    }

    return props.isMerchantLocks
      ? ''
      : t('debit-card-item.merchant-category-restriction.warning-note.message');
  });

  const isSelectedLabelVisible = computed(() => (isMasterCardFlow?: boolean) => {
    if (isMasterCardFlow) {
      return !!selectedMerchantCategoryUuid.value;
    }

    return !!selectedMerchantCategoryUuids.value.length;
  });

  const handleFetchCardMerchantCategories = async (
    endpoint: string,
    page: number,
    done: (value: boolean) => void,
    searchKeyword?: string,
    isMasterCardFlow?: boolean,
  ) => {
    if (isListEmpty.value) {
      /* istanbul ignore next -- @preserve */
      return;
    }

    isFetching.value = true;

    // Check if there are any previous pending requests
    if (cancelToken.value !== undefined) {
      /* istanbul ignore next -- @preserve */
      cancelToken.value.cancel();
    }

    // Save the cancel token for the current request
    cancelToken.value = axios.CancelToken.source();

    const response = await fetchCardMerchantCategories(
      endpoint,
      page,
      cancelToken.value,
      searchKeyword,
      undefined,
      isMasterCardFlow,
    );

    /* istanbul ignore next -- @preserve */
    if (!response?.data?.length) {
      isFetching.value = false;

      return;
    }

    localMerchantCategories.value = [
      ...localMerchantCategories.value,
      ...response.data,
    ];

    cancelToken.value = undefined;

    if (done) {
      done(response.current_page >= response.last_page);
    }

    isFetching.value = false;
  };

  const resetCardMerchantCategories = () => {
    localMerchantCategories.value = [];
    isListEmpty.value = false;
    listChangedCount.value += 1;
  };

  const removeSelectedMerchantCategoryUuid = (uuid: Merchant['uuid']) => {
    selectedMerchantCategoryUuids.value = selectedMerchantCategoryUuids.value.filter(
      (selectedUuid) => selectedUuid !== uuid,
    );

    categoriesCount.value -= 1;

    if (areAllMerchantCategoriesSelected.value !== null
      && areAllMerchantCategoriesSelected.value
      && selectedMerchantCategoryUuids.value.length) {
      areAllMerchantCategoriesSelected.value = null;
    } else if (!selectedMerchantCategoryUuids.value.length
      && areAllMerchantCategoriesSelected.value === null) {
      areAllMerchantCategoriesSelected.value = false;
    }
  };

  const fetchTotalMerchantCategories = async (
    endpoint: string,
    searchKeyword?: string,
    isMasterCardFlow?: boolean,
  ) => {
    if (totalCancelToken.value !== undefined) {
      /* istanbul ignore next -- @preserve */
      totalCancelToken.value.cancel();
    }

    // Save the cancel token for the current request
    totalCancelToken.value = axios.CancelToken.source();

    const response = await fetchCardMerchantCategories(
      endpoint,
      1,
      totalCancelToken.value,
      searchKeyword,
      ['uuid'],
      isMasterCardFlow,
    );

    if (!response?.data?.length) {
      /* istanbul ignore next -- @preserve */
      return;
    }

    categoriesTotal.value = response.total;
    categoriesCount.value = categoriesTotal.value;

    totalCancelToken.value = undefined;
  };

  const setInitialTotalMerchantCategories = (
    endpoint: string,
    merchantCategoriesCount: number,
    searchText: string,
    isMasterCardFlow?: boolean,
  ): void => {
    if (!merchantCategoriesCount) {
      void fetchTotalMerchantCategories(endpoint, searchText, isMasterCardFlow);
    }
  };

  const prefillMerchantCategoryUuids = (
    merchantCategoryUuids?: Merchant['uuid'][],
    isMasterCardFlow?: boolean,
  ): void => {
    if (!merchantCategoryUuids) {
      return;
    }

    if (isMasterCardFlow) {
      const [firstUUid] = merchantCategoryUuids;

      selectedMerchantCategoryUuid.value = firstUUid ?? '';
    } else {
      selectedMerchantCategoryUuids.value = merchantCategoryUuids;
    }
  };

  const prefillCategoriesCount = (count: number): void => {
    categoriesCount.value = count;
  };

  return {
    isFetching,
    localMerchantCategories,
    listChangedCount,
    selectedMerchantCategoryUuid,
    selectedMerchantCategoryUuids,
    areAllMerchantCategoriesSelected,
    categoriesCount,
    allCardMerchantCategories,
    excludeSelectedMerchantCategoryUuids,
    categoriesTotal,
    isHideChoiceChips,
    isSelectedLabelVisible,
    shouldShowWarningBanner,
    selectedMerchantCategory,
    isErrorBannerVisible,
    warningBannerMessage,
    selectedSingleMerchantCategory,
    handleFetchCardMerchantCategories,
    resetCardMerchantCategories,
    removeSelectedMerchantCategoryUuid,
    fetchTotalMerchantCategories,
    setInitialTotalMerchantCategories,
    prefillMerchantCategoryUuids,
    prefillCategoriesCount,
  };
};

export default useCardMerchantCategoryList;
