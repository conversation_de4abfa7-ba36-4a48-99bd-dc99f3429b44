import { computed } from 'vue';
import useUtility from 'src/composables/useUtility.ts';
import useCardSetting from 'features/card/composables/useCardSetting.ts';
import {
  type CardMerchantCategoryConfig,
  CardMerchantCategoryRestriction,
  type CardMerchantCategoryRestrictionOption,
  MerchantType,
} from 'features/card/types/CardMerchantCategory.ts';

const useCardMerchantCategoryConfig = () => {
  const { t } = useUtility();
  const { isMerchantLocksRevampEnabled, isMerchantCategoryLocksEnabled } = useCardSetting();

  // Static configuration for merchant category locks
  const merchantCategoryConfig: CardMerchantCategoryConfig = {
    title: 'debit-cards.debit-card-item.merchant-category-restriction-widget.label',
    subTitle: 'debit-card-item.merchant-category-restriction.selection-info-subtitle',
    tooltipContent: 'debit-cards.debit-card-item.merchant-category-restriction-widget.tooltip',
    placeholderRestriction: 'debit-card-item.merchant-restriction-widget.select-allow-block',
    selectedCategoryLabel: 'debit-card-item.merchant-category-restriction.select-modal-value',
    placeholderCategory: 'debit-card-item.merchant-category-restriction-widget.select-placeholder',
    titleRestrictionModal: 'debit-card-item.merchant-category-restriction-widget.select-allow--block-modal-title',
    searchPlaceholder: 'debit-card-item.merchant-category-restriction.select-modal-search-title',
    selectAllLabel: 'debit-card-item.merchant-category-restriction.select-modal-select-all-cta',
    titleSelectModal: 'debit-card-item.merchant-category-restriction.select-modal-title',
    subTitleSelectModal: 'debit-card-item.merchant-category-restriction.single-select-modal-subtitle',
    confirmButtonLabel: 'debit-card-item.merchant-category-restriction.select-modal-disable-cta-disable',
  };

  // Static configuration for merchant locks
  const merchantLocksConfig: CardMerchantCategoryConfig = {
    title: 'debit-cards.debit-card-item.merchant-restriction-widget.label',
    subTitle: 'debit-card-item.merchant-restriction.selection-info-subtitle',
    tooltipContent: 'debit-cards.debit-card-item.merchant-restriction-widget.tooltip',
    placeholderRestriction: 'debit-card-item.merchant-restriction-widget.select-allow-block',
    selectedCategoryLabel: 'debit-card-item.merchant-category-restriction.select-modal-value',
    placeholderCategory: 'debit-card-item.merchant-restriction-widget.select-merchants',
    titleRestrictionModal: 'debit-card-item.merchant-restriction-widget.select-allow--block-modal-title',
    searchPlaceholder: 'debit-card-item.merchant-restriction.select-modal-search-title',
    selectAllLabel: 'debit-card-item.merchant-restriction.select-modal-select-all-cta',
    titleSelectModal: 'debit-card-item.merchant-restriction.detail.modal-title',
    subTitleSelectModal: 'debit-card-item.merchant-restriction.single-select-modal-subtitle',
    confirmButtonLabel: 'debit-card-item.merchant-restriction.select-modal-save-cta',
  };

  // Restriction options
  const niumMerchantCategoryRestrictionOptions: CardMerchantCategoryRestrictionOption[] = [
    {
      name: t('debit-card-item.merchant-category-restriction-widget.select-allow-title', 1),
      description: t('debit-card-item.merchant-category-restriction-widget.select-allow-subtitle', 1),
      value: CardMerchantCategoryRestriction.ALLOW,
    },
    {
      name: t('debit-card-item.merchant-category-restriction-widget.select-block-title', 1),
      description: t('debit-card-item.merchant-category-restriction-widget.select-block-subtitle', 1),
      value: CardMerchantCategoryRestriction.DENY,
    },
  ];

  const niumMerchantRestrictionOptions: CardMerchantCategoryRestrictionOption[] = [
    {
      name: t('debit-card-item.merchant-restriction-widget.select-allow-title', 1),
      description: t('debit-card-item.merchant-restriction-widget.select-allow-subtitle', 1),
      value: CardMerchantCategoryRestriction.ALLOW,
    },
    {
      name: t('debit-card-item.merchant-restriction-widget.select-block-title', 1),
      description: t('debit-card-item.merchant-restriction-widget.select-block-subtitle', 1),
      value: CardMerchantCategoryRestriction.DENY,
    },
  ];

  const mastercardMerchantCategoryRestrictionOptions: CardMerchantCategoryRestrictionOption[] = [
    {
      name: t('debit-card-item.merchant-category-restriction-widget.select-allow-title', 0),
      description: t('debit-card-item.merchant-category-restriction-widget.select-allow-subtitle', 0),
      value: CardMerchantCategoryRestriction.ALLOW,
    },
    {
      name: t('debit-card-item.merchant-category-restriction-widget.select-block-title', 0),
      description: t('debit-card-item.merchant-category-restriction-widget.select-block-subtitle', 0),
      value: CardMerchantCategoryRestriction.DENY,
    },
  ];

  const mastercardMerchantRestrictionOptions: CardMerchantCategoryRestrictionOption[] = [
    {
      name: t('debit-card-item.merchant-restriction-widget.select-allow-title', 0),
      description: t('debit-card-item.merchant-restriction-widget.select-allow-subtitle', 0),
      value: CardMerchantCategoryRestriction.ALLOW,
    },
    {
      name: t('debit-card-item.merchant-restriction-widget.select-block-title', 0),
      description: t('debit-card-item.merchant-restriction-widget.select-block-subtitle', 0),
      value: CardMerchantCategoryRestriction.DENY,
    },
  ];

  // Computed getters
  const getConfigByType = computed(
    () => (type: MerchantType): CardMerchantCategoryConfig => (type === MerchantType.MERCHANT_LOCKS
      ? merchantLocksConfig
      : merchantCategoryConfig),
  );

  const getMerchantCategoryRestrictionOptions = computed(() => (
    isMasterCard: boolean,
  ): CardMerchantCategoryRestrictionOption[] => (isMasterCard
    ? mastercardMerchantCategoryRestrictionOptions
    : niumMerchantCategoryRestrictionOptions));

  const getMerchantRestrictionOptions = computed(() => (
    isMasterCard: boolean,
  ): CardMerchantCategoryRestrictionOption[] => (isMasterCard
    ? mastercardMerchantRestrictionOptions
    : niumMerchantRestrictionOptions));

  const getRestrictionOptions = computed(() => (
    type: MerchantType,
    isMasterCard: boolean,
  ): CardMerchantCategoryRestrictionOption[] => (type === MerchantType.MERCHANT_LOCKS
    ? getMerchantRestrictionOptions.value(isMasterCard)
    : getMerchantCategoryRestrictionOptions.value(isMasterCard)));

  const isMerchantCategoryRevampVisible = computed(
    () => isMerchantLocksRevampEnabled.value && isMerchantCategoryLocksEnabled.value,
  );

  const debitCardsDetailMerchantApiFields = computed(() => (isMerchantLocksRevampEnabled.value
    ? [
      'merchant_category_lock_restriction_type',
      'merchant_category_locks.uuid',
      'merchant_category_locks.name',
      'merchant_category_locks.logo_url',
      'merchant_lock_restriction_type',
      'merchant_locks.uuid',
      'merchant_locks.logo_url',
      'merchant_locks.name',
    ]
    : []));

  // Pure functions for finding restriction options
  const findRestrictionOption = (
    type: MerchantType,
    isMasterCardFlow: boolean,
    restrictionType: string,
  ): CardMerchantCategoryRestrictionOption | undefined => {
    const options = getRestrictionOptions.value(type, isMasterCardFlow);

    return options.find((option) => option.value === restrictionType);
  };

  return {
    // Configurations
    merchantCategoryConfig,
    merchantLocksConfig,
    getConfigByType,

    // Restriction options
    getMerchantCategoryRestrictionOptions,
    getMerchantRestrictionOptions,
    getRestrictionOptions,
    findRestrictionOption,

    // Computed properties
    isMerchantCategoryRevampVisible,
    debitCardsDetailMerchantApiFields,
  };
};

export default useCardMerchantCategoryConfig;
