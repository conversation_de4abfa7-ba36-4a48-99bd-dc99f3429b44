import axios, { type CancelTokenSource } from 'axios';
import { ref } from 'vue';
import { useCardMerchantCategoryStore } from 'src/stores/cardMerchantCategoryStore.ts';
import useCardApi from 'features/card/composables/useCardApi.ts';
import { MerchantType } from 'features/card/types/CardMerchantCategory.ts';

const useMerchantCategoryApi = () => {
  const { fetchCardMerchantCategories } = useCardApi();
  const store = useCardMerchantCategoryStore();

  // Cancel tokens for each merchant type
  const cancelTokens = ref<Record<MerchantType, CancelTokenSource | undefined>>({
    [MerchantType.MERCHANT_CATEGORY_LOCKS]: undefined,
    [MerchantType.MERCHANT_LOCKS]: undefined,
  });

  const totalCancelTokens = ref<Record<MerchantType, CancelTokenSource | undefined>>({
    [MerchantType.MERCHANT_CATEGORY_LOCKS]: undefined,
    [MerchantType.MERCHANT_LOCKS]: undefined,
  });

  const handleFetchCardMerchantCategories = async (
    type: MerchantType,
    endpoint: string,
    page: number,
    done: (value: boolean) => void,
    searchKeyword?: string,
    isMasterCardFlow?: boolean,
  ): Promise<void> => {
    const localCategories = store.getLocalMerchantCategories(type);

    if (!localCategories.length && page > 1) {
      return;
    }

    store.setIsFetching(type, true);

    // Check if there are any previous pending requests
    if (cancelTokens.value[type] !== undefined) {
      cancelTokens.value[type]?.cancel();
    }

    // Save the cancel token for the current request
    cancelTokens.value[type] = axios.CancelToken.source();

    try {
      const response = await fetchCardMerchantCategories(
        endpoint,
        page,
        cancelTokens.value[type],
        searchKeyword,
        undefined,
        isMasterCardFlow,
      );

      if (!response?.data?.length) {
        store.setIsFetching(type, false);

        return;
      }

      store.appendLocalMerchantCategories(type, response.data);

      cancelTokens.value[type] = undefined;

      if (done) {
        done(response.current_page >= response.last_page);
      }
    } catch (error) {
      // Handle API errors
      if (!axios.isCancel(error)) {
        console.error('Error fetching merchant categories:', error);
      }
    } finally {
      store.setIsFetching(type, false);
    }
  };

  const fetchTotalMerchantCategories = async (
    type: MerchantType,
    endpoint: string,
    searchKeyword?: string,
    isMasterCardFlow?: boolean,
  ): Promise<void> => {
    if (totalCancelTokens.value[type] !== undefined) {
      totalCancelTokens.value[type]?.cancel();
    }

    // Save the cancel token for the current request
    totalCancelTokens.value[type] = axios.CancelToken.source();

    try {
      const response = await fetchCardMerchantCategories(
        endpoint,
        1,
        totalCancelTokens.value[type],
        searchKeyword,
        ['uuid'],
        isMasterCardFlow,
      );

      if (!response?.data?.length) {
        return;
      }

      store.setCategoriesTotal(type, response.total);
      store.setCategoriesCount(type, response.total);

      totalCancelTokens.value[type] = undefined;
    } catch (error) {
      // Handle API errors
      if (!axios.isCancel(error)) {
        console.error('Error fetching total merchant categories:', error);
      }
    }
  };

  const setInitialTotalMerchantCategories = async (
    type: MerchantType,
    endpoint: string,
    merchantCategoriesCount: number,
    searchText: string,
    isMasterCardFlow?: boolean,
  ): Promise<void> => {
    if (!merchantCategoriesCount) {
      await fetchTotalMerchantCategories(type, endpoint, searchText, isMasterCardFlow);
    }
  };

  const resetCardMerchantCategories = (type: MerchantType): void => {
    store.resetLocalMerchantCategories(type);
  };

  const cancelAllRequests = (type: MerchantType): void => {
    if (cancelTokens.value[type]) {
      cancelTokens.value[type]?.cancel();
      cancelTokens.value[type] = undefined;
    }

    if (totalCancelTokens.value[type]) {
      totalCancelTokens.value[type]?.cancel();
      totalCancelTokens.value[type] = undefined;
    }
  };

  return {
    handleFetchCardMerchantCategories,
    fetchTotalMerchantCategories,
    setInitialTotalMerchantCategories,
    resetCardMerchantCategories,
    cancelAllRequests,
  };
};

export default useMerchantCategoryApi;
