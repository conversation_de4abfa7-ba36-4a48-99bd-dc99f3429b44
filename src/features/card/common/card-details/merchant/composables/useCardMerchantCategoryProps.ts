import { computed, type Ref } from 'vue';
import useCardMerchantCategorySettings from 'features/card/common/card-details/merchant/composables/useCardMerchantCategorySettings.ts';

const useCardMerchantCategoryProps = (isMasterCardFlow: Ref<boolean>) => {
  const {
    getMastercardMerchantCategoriesLocksVisible,
    getMerchantLocksRevampSectionVisible,
    getMerchantCategoryLocksFieldVisible,
    getMastercardMerchantLocksVisible,
  } = useCardMerchantCategorySettings();

  const isMerchantLocksRevampSectionVisible = computed(
    () => getMerchantLocksRevampSectionVisible(isMasterCardFlow.value),
  );
  const isMerchantCategoryLocksFieldVisible = computed(
    () => getMerchantCategoryLocksFieldVisible(isMasterCardFlow.value),
  );
  const isMastercardMerchantCategoriesLocksVisible = computed(
    () => getMastercardMerchantCategoriesLocksVisible(isMasterCardFlow.value),
  );
  const isMastercardMerchantLocksVisible = computed(
    () => getMastercardMerchantLocksVisible(isMasterCardFlow.value),
  );

  return {
    isMerchantLocksRevampSectionVisible,
    isMerchantCategoryLocksFieldVisible,
    isMastercardMerchantCategoriesLocksVisible,
    isMastercardMerchantLocksVisible,
  };
};

export default useCardMerchantCategoryProps;
