import { computed, ref } from 'vue';
import useUtility from 'src/composables/useUtility.ts';
import useCardMerchantCategorySettings from 'features/card/common/card-details/merchant/composables/useCardMerchantCategorySettings.ts';
import useCardSetting from 'features/card/composables/useCardSetting.ts';
import {
  type CardFormMerchantCategory,
  CardMerchantCategoryRestriction,
  type CardMerchantCategoryRestrictionOption,
} from 'features/card/types/CardMerchantCategory.ts';
import { type Merchant } from 'features/debit/types/Merchant.ts';

const useCardMerchantCategory = () => {
  const { t } = useUtility();
  const { isMerchantLocksRevampEnabled, isMerchantCategoryLocksEnabled } = useCardSetting();
  const { getMerchantLocksRevampSectionVisible } = useCardMerchantCategorySettings();

  const isCategoryModalVisible = ref(false);
  const isRestrictionsModalVisible = ref(false);
  const searchText = ref('');
  const selectedCategories = ref<Merchant[]>([]);
  const selectedRestrictionOption = ref<CardMerchantCategoryRestrictionOption>();

  const merchantCategoryProps = {
    title: 'debit-cards.debit-card-item.merchant-category-restriction-widget.label',
    subTitle: 'debit-card-item.merchant-category-restriction.selection-info-subtitle',
    tooltipContent: 'debit-cards.debit-card-item.merchant-category-restriction-widget.tooltip',
    placeholderRestriction: 'debit-card-item.merchant-restriction-widget.select-allow-block',
    selectedCategoryLabel: 'debit-card-item.merchant-category-restriction.select-modal-value',
    placeholderCategory: 'debit-card-item.merchant-category-restriction-widget.select-placeholder',
    titleRestrictionModal: 'debit-card-item.merchant-category-restriction-widget.select-allow--block-modal-title',
    searchPlaceholder: 'debit-card-item.merchant-category-restriction.select-modal-search-title',
    selectAllLabel: 'debit-card-item.merchant-category-restriction.select-modal-select-all-cta',
    titleSelectModal: 'debit-card-item.merchant-category-restriction.select-modal-title',
    subTitleSelectModal: 'debit-card-item.merchant-category-restriction.single-select-modal-subtitle',
    confirmButtonLabel: 'debit-card-item.merchant-category-restriction.select-modal-disable-cta-disable',
  };

  const merchantLocksProps = {
    title: 'debit-cards.debit-card-item.merchant-restriction-widget.label',
    subTitle: 'debit-card-item.merchant-restriction.selection-info-subtitle',
    tooltipContent: 'debit-cards.debit-card-item.merchant-restriction-widget.tooltip',
    placeholderRestriction: 'debit-card-item.merchant-restriction-widget.select-allow-block',
    selectedCategoryLabel: 'debit-card-item.merchant-category-restriction.select-modal-value',
    placeholderCategory: 'debit-card-item.merchant-restriction-widget.select-merchants',
    titleRestrictionModal: 'debit-card-item.merchant-restriction-widget.select-allow--block-modal-title',
    searchPlaceholder: 'debit-card-item.merchant-restriction.select-modal-search-title',
    selectAllLabel: 'debit-card-item.merchant-restriction.select-modal-select-all-cta',
    titleSelectModal: 'debit-card-item.merchant-restriction.detail.modal-title',
    subTitleSelectModal: 'debit-card-item.merchant-restriction.single-select-modal-subtitle',
    confirmButtonLabel: 'debit-card-item.merchant-restriction.select-modal-save-cta',
  };

  const NIUM_MERCHANT_CATEGORY_RESTRICTION_OPTIONS: CardMerchantCategoryRestrictionOption[] = [
    {
      name: t('debit-card-item.merchant-category-restriction-widget.select-allow-title', 1),
      description: t('debit-card-item.merchant-category-restriction-widget.select-allow-subtitle', 1),
      value: CardMerchantCategoryRestriction.ALLOW,
    },
    {
      name: t('debit-card-item.merchant-category-restriction-widget.select-block-title', 1),
      description: t('debit-card-item.merchant-category-restriction-widget.select-block-subtitle', 1),
      value: CardMerchantCategoryRestriction.DENY,
    },
  ];

  const NIUM_MERCHANT_RESTRICTION_OPTIONS: CardMerchantCategoryRestrictionOption[] = [
    {
      name: t('debit-card-item.merchant-restriction-widget.select-allow-title', 1),
      description: t('debit-card-item.merchant-restriction-widget.select-allow-subtitle', 1),
      value: CardMerchantCategoryRestriction.ALLOW,
    },
    {
      name: t('debit-card-item.merchant-restriction-widget.select-block-title', 1),
      description: t('debit-card-item.merchant-restriction-widget.select-block-subtitle', 1),
      value: CardMerchantCategoryRestriction.DENY,
    },
  ];

  const MASTERCARD_MERCHANT_CATEGORY_RESTRICTION_OPTIONS
    : CardMerchantCategoryRestrictionOption[] = [
      {
        name: t('debit-card-item.merchant-category-restriction-widget.select-allow-title', 0),
        description: t('debit-card-item.merchant-category-restriction-widget.select-allow-subtitle', 0),
        value: CardMerchantCategoryRestriction.ALLOW,
      },
      {
        name: t('debit-card-item.merchant-category-restriction-widget.select-block-title', 0),
        description: t('debit-card-item.merchant-category-restriction-widget.select-block-subtitle', 0),
        value: CardMerchantCategoryRestriction.DENY,
      },
    ];

  const MASTERCARD_MERCHANT_RESTRICTION_OPTIONS: CardMerchantCategoryRestrictionOption[] = [
    {
      name: t('debit-card-item.merchant-restriction-widget.select-allow-title', 0),
      description: t('debit-card-item.merchant-restriction-widget.select-allow-subtitle', 0),
      value: CardMerchantCategoryRestriction.ALLOW,
    },
    {
      name: t('debit-card-item.merchant-restriction-widget.select-block-title', 0),
      description: t('debit-card-item.merchant-restriction-widget.select-block-subtitle', 0),
      value: CardMerchantCategoryRestriction.DENY,
    },
  ];

  const DEBIT_CARDS_DETAIL_MERCHANT_API_FIELDS = isMerchantLocksRevampEnabled.value
    ? [
      'merchant_category_lock_restriction_type',
      'merchant_category_locks.uuid',
      'merchant_category_locks.name',
      'merchant_category_locks.logo_url',
      'merchant_lock_restriction_type',
      'merchant_locks.uuid',
      'merchant_locks.logo_url',
      'merchant_locks.name',
    ]
    : [];

  const isMerchantCategoryRevampVisible = computed(
    () => isMerchantLocksRevampEnabled.value && isMerchantCategoryLocksEnabled.value,
  );

  const merchantCategoryRestrictionOptions = computed(() => (isMasterCard: boolean) => {
    if (isMasterCard) {
      return MASTERCARD_MERCHANT_CATEGORY_RESTRICTION_OPTIONS;
    }

    return NIUM_MERCHANT_CATEGORY_RESTRICTION_OPTIONS;
  });

  const merchantRestrictionOptions = computed(() => (isMasterCard: boolean) => {
    if (isMasterCard) {
      return MASTERCARD_MERCHANT_RESTRICTION_OPTIONS;
    }

    return NIUM_MERCHANT_RESTRICTION_OPTIONS;
  });

  const cardDetailsCategoryLocksTitle = computed(() => (
    type?: CardMerchantCategoryRestriction,
    countCategoryLocks = 1,
  ) => (type === CardMerchantCategoryRestriction.ALLOW
    ? t('debit-card-item.merchant-category-restriction.detail.allowed-title', countCategoryLocks)
    : t('debit-card-item.merchant-category-restriction.detail.blocked-title', countCategoryLocks)));

  const cardDetailsMerchantLocksTitle = computed(() => (
    type?: CardMerchantCategoryRestriction,
    countCategoryLocks = 1,
  ) => (type === CardMerchantCategoryRestriction.ALLOW
    ? t('debit-card-item.merchant-restriction.detail.allowed-title', countCategoryLocks)
    : t('debit-card-item.merchant-restriction.detail.blocked-title', countCategoryLocks)));

  const cardDetailsCategoryLocksSubTitle = computed(() => (
    type?: CardMerchantCategoryRestriction,
    countCategoryLocks = 1,
  ) => (type === CardMerchantCategoryRestriction.ALLOW
    ? t('debit-card-item.merchant-category-restriction.detail.allowed-modal-subtitle', countCategoryLocks)
    : t('debit-card-item.merchant-category-restriction.detail.blocked-modal-subtitle', countCategoryLocks)));

  const cardDetailsMerchantLocksSubTitle = computed(() => (
    type?: CardMerchantCategoryRestriction,
    countCategoryLocks = 1,
  ) => (type === CardMerchantCategoryRestriction.ALLOW
    ? t('debit-card-item.merchant-restriction.detail.allowed-modal-subtitle', countCategoryLocks)
    : t('debit-card-item.merchant-restriction.detail.blocked-modal-subtitle', countCategoryLocks)));

  const cardDetailsCategoryLocksTooltip = computed(() => (
    type?: CardMerchantCategoryRestriction,
    countCategoryLocks = 1,
  ) => (type === CardMerchantCategoryRestriction.ALLOW
    ? t('debit-card-item.merchant-category-restriction.detail.allowed-tooltip', countCategoryLocks)
    : t('debit-card-item.merchant-category-restriction.detail.blocked-tooltip', countCategoryLocks)));

  const cardDetailsMerchantLocksTooltip = computed(() => (
    type?: CardMerchantCategoryRestriction,
    countCategoryLocks = 1,
  ) => (type === CardMerchantCategoryRestriction.ALLOW
    ? t('debit-card-item.merchant-restriction.detail.allowed-tooltip', countCategoryLocks)
    : t('debit-card-item.merchant-restriction.detail.blocked-tooltip', countCategoryLocks)));

  const cardDetailsCategoryLocksValue = computed(() => (
    categories?: Merchant[],
  ) => (Number(categories?.length) > 1
    ? t('debit-card-item.merchant-category-restriction.detail.selected-text', { count: Number(categories?.length) })
    : categories?.[0]?.name ?? ''));

  const onSelectRestrictionOption = (option: CardMerchantCategoryRestrictionOption) => {
    selectedRestrictionOption.value = option;
    isRestrictionsModalVisible.value = false;
  };

  const prefillMerchantRestrictionType = (
    isMasterCardFlow: boolean,
    merchantRestrictionType?: string,
    isMerchantLocks?: boolean,
  ): void => {
    if (!merchantRestrictionType) {
      /* istanbul ignore next -- @preserve */
      return;
    }

    const options = !isMerchantLocks
      ? merchantCategoryRestrictionOptions.value(isMasterCardFlow)
      : merchantRestrictionOptions.value(isMasterCardFlow);

    selectedRestrictionOption.value = options
      .find((option) => option.value === merchantRestrictionType);
  };

  const merchantCategoryLocksQueryParams = computed(() => (
    form: CardFormMerchantCategory,
  ) => {
    if (!isMerchantCategoryRevampVisible.value) {
      /* istanbul ignore next -- @preserve */
      return {};
    }

    const {
      areAllMerchantCategorySelected, merchantCategoryRestrictionType,
      merchantCategoryUuids, merchantCategorySearchText, merchantCategoryExcludeUuids,
      isMasterCardFlow,
    } = form;

    if (!merchantCategoryRestrictionType) {
      return {};
    }

    const params: Record<string, unknown> = {};

    params.all_records_merchant_categories = !isMasterCardFlow
      ? areAllMerchantCategorySelected
      : false;

    if (merchantCategoryRestrictionType) {
      params.merchant_category_lock_restriction_type = merchantCategoryRestrictionType;
    }

    if (merchantCategoryUuids?.length
      && !params.all_records_merchant_categories) {
      params.merchant_category_locks_uuids = merchantCategoryUuids;
    }

    if (merchantCategorySearchText) {
      params.search_merchant_categories = merchantCategorySearchText;
    }

    if (merchantCategoryExcludeUuids?.length && !isMasterCardFlow) {
      params.merchant_category_locks_exclude_uuids = merchantCategoryExcludeUuids;
    }

    return params;
  });

  const merchantLocksQueryParams = computed(() => (
    form: CardFormMerchantCategory,
  ) => {
    if (!(isMerchantLocksRevampEnabled.value
      && getMerchantLocksRevampSectionVisible(!!form.isMasterCardFlow)
    )) {
      return {};
    }

    const {
      areAllMerchantSelected, merchantRestrictionType,
      merchantUuids, merchantSearchText, merchantExcludeUuids,
      isMasterCardFlow,
    } = form;

    if (!merchantRestrictionType) {
      return {};
    }

    const params: Record<string, unknown> = {};

    params.all_records_merchant_locks = !isMasterCardFlow
      ? areAllMerchantSelected
      : false;

    if (merchantRestrictionType) {
      params.merchant_lock_restriction_type = merchantRestrictionType;
    }

    if (merchantUuids?.length
      && !params.all_records_merchant_locks) {
      params.merchant_locks_uuids = merchantUuids;
    }

    if (merchantSearchText) {
      params.search_merchant_locks = merchantSearchText;
    }

    if (merchantExcludeUuids?.length && !isMasterCardFlow) {
      params.merchant_locks_exclude_uuids = merchantExcludeUuids;
    }

    return params;
  });

  const disableMerchantCategoryLocks = computed(() => (
    isMerchantCategoryEnabled?: boolean | null,
  ) => {
    if (!isMerchantLocksRevampEnabled.value
      || isMerchantCategoryEnabled) {
      return {};
    }

    const params: Record<string, unknown> = {};

    params.all_records_merchant_categories = false;
    params.merchant_category_lock_restriction_type = null;
    params.merchant_category_locks_uuids = [];
    params.search_merchant_categories = null;
    params.merchant_category_locks_exclude_uuids = [];

    return params;
  });

  const disableMerchantLocks = computed(() => (isMerchantLocksEnabled?: boolean | null) => {
    if (!isMerchantLocksRevampEnabled.value
      || isMerchantLocksEnabled) {
      return {};
    }

    const params: Record<string, unknown> = {};

    params.all_records_merchant_locks = false;
    params.merchant_lock_restriction_type = null;
    params.merchant_locks_uuids = [];
    params.search_merchant_locks = null;
    params.merchant_locks_exclude_uuids = [];

    return params;
  });

  return {
    DEBIT_CARDS_DETAIL_MERCHANT_API_FIELDS,
    isMerchantCategoryRevampVisible,
    isCategoryModalVisible,
    searchText,
    selectedCategories,
    isRestrictionsModalVisible,
    selectedRestrictionOption,
    merchantCategoryRestrictionOptions,
    merchantRestrictionOptions,
    cardDetailsCategoryLocksTitle,
    cardDetailsCategoryLocksValue,
    cardDetailsCategoryLocksSubTitle,
    cardDetailsCategoryLocksTooltip,
    merchantCategoryLocksQueryParams,
    merchantLocksQueryParams,
    merchantCategoryProps,
    merchantLocksProps,
    cardDetailsMerchantLocksTitle,
    cardDetailsMerchantLocksSubTitle,
    cardDetailsMerchantLocksTooltip,
    disableMerchantCategoryLocks,
    disableMerchantLocks,
    onSelectRestrictionOption,
    prefillMerchantRestrictionType,
  };
};

export default useCardMerchantCategory;
